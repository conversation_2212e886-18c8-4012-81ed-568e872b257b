import { api } from '@/api';
import { useStore } from '@/store';
// import { queryDictDataList } from '@/views/risk-mgr/risk-divide/divideTemplate/fetchData';
// import { getControlConfig } from '@/views/risk-mgr/risk-identc-list/listTemplate/fetchData.ts';
import ImageCompressor from 'image-compressor.js';
import type { OptionalParams as fileOptionalParams } from 'js-file-downloader';
import JsFileDownloader from 'js-file-downloader';
import { stringify } from 'querystringify';
const dict = useStore();
/**
 * 字典函数
 */
// export async function dictDate(type: string) {
//   if (dict.dictOption[type].length) {
//     return dict.dictOption[type];
//   }
//   const params = {
//     dictType: 'dict_risk_point_type',
//   };
//   if (type == 'fxdType') params.dictType = 'dict_risk_point_type';
//   if (type == 'pgffType') params.dictType = 'dict_risk_evaluation_method';
//   if (type == 'xzffType') params.dictType = 'dict_risk_level';
//   if (type == 'bsztType') params.dictType = 'dict_risk_identification_status';
//   if (type == 'gyfxType') params.dictType = 'dict_risk_level';
//   if (type == 'fxdjType') params.dictType = 'dict_risk_level';
//   if (type == 'fxcztType') params.dictType = 'dict_risk_notify_card_status';
//   if (type == 'gkzhType') params.dictType = 'dict_yes_no'; //管控状态

//   const res = await queryDictDataList(params);
//   const _RES: any = res?.data || [];
//   dict.dictOption[type] = _RES;
//   return _RES;
// }

/**
 * 转义dictValue输出dictLabel, type 类型
 */
// export function getDictName(type: string, dictValue: string) {
//   if (!dictValue) return dictValue;
//   const typeArray = dict.dictOption[type];

//   if (typeArray?.length) {
//     let label = dictValue;
//     for (let index = 0; index < typeArray.length; index++) {
//       const element: any = typeArray[index];
//       if (element.dictValue == dictValue) {
//         label = element.dictLabel;
//       }
//     }
//     return label;
//   } else {
//     const params = {
//       dictType: '',
//     };
//     if (type == 'fxdType') params.dictType = 'dict_risk_point_type';
//     if (type == 'pgffType') params.dictType = 'dict_risk_evaluation_method';
//     if (type == 'xzffType') params.dictType = 'dict_risk_point_type';
//     if (type == 'bsztType') params.dictType = 'dict_risk_identification_status';
//     if (type == 'gyfxType') params.dictType = 'dict_risk_level';

//     if (type == 'fxdjType') params.dictType = 'dict_risk_level';
//     if (type == 'fxcztType') params.dictType = 'dict_risk_notify_card_status';
//     if (type == 'gkzhType') params.dictType = 'dict_yes_no';
//     queryDictDataList(params).then((res) => {
//       const _RES: any = res?.data || [];
//       dict.dictOption[type] = _RES;
//       getDictName(type, dictValue);
//     });
//   }
// }

/**
 * 导出转文件
 */
export function downloadFile(larams: any, urlName: string) {
  const xhr: any = new XMLHttpRequest();
  const paramsStr = larams ? `?${stringify(larams)}` : '';
  console.log(paramsStr, '-----------');
  xhr.open('POST', `${window.$SYS_CFG.apiBaseURL}/${api.type.exam}${urlName}${paramsStr}`, true);
  xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');
  xhr.responseType = 'blob';
  xhr.onload = function (e: any) {
    if (this.status == 200) {
      const blob = this.response;
      const a = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      //获取后端文件名称
      const fileName: any = decodeURI(xhr.getResponseHeader('content-disposition'));
      //截取=字符串后面的内容
      const str = fileName.match(/=(\S*)/)[1];
      a.download = str;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };
  //参数是json格式
  xhr.send(JSON.stringify(larams));
}

const isURLEncoded = (str: string) => /%[0-9A-Fa-f]{2}/g.test(str);
/**
 * 文件下载
 * @param url
 * @param options
 */
export function fileDownloader(url: string, options: fileOptionalParams = {}) {
  const defaultOpt: fileOptionalParams = {
    timeout: 2 * 60 * 1000,
    contentTypeDetermination: 'header',
    headers: [],
  };

  return new JsFileDownloader({
    url,
    ...defaultOpt,
    ...options,
    nameCallback: (name: string) => {
      // 处理中文乱码问题
      if (isURLEncoded(name)) name = decodeURIComponent(name);
      console.log(name);
      console.log(options.filename);
      return options.filename || name;
    },
  });
}

/**
 * 传入riskLevel查询角色id
 * 通过角色id查询所有人员
 * 对象存储，一个角色只查询一次
 */

// export function getUsefilter(levelCode: string) {
//   const levelArray = dict.routerUser.levelArray;
//   if (!levelArray.length) {
//     const params = {
//       unitId: localStorage.getItem('_riskUnitID'),
//     };
//     getControlConfig(params).then((res) => {
//       const data: any = res?.data;
//       const _RES: any = data?.levelControlLevelConfigList || [];
//       dict.routerUser.levelArray = _RES;
//       getUsefilter(levelCode);
//     });
//   } else {
//     let routeId = '';
//     for (let index = 0; index < levelArray.length; index++) {
//       const element = levelArray[index];
//       if (element.riskLevel == levelCode && element.responsibleJson) {
//         routeId =
//           element.responsibleJson &&
//           JSON.parse(element.responsibleJson).length &&
//           JSON.parse(element.responsibleJson)[0]?.dataId;
//       }
//     }
//     if (!routeId) return '';
//     // const routeIDArray = dict.routerUser[routeId];
//     // // if (!routeIDArray.length) {
//     // const para = {
//     //   orgCode: localStorage.getItem('_riskUnitID'),
//     //   roleIds: routeId,
//     //   pageNo: 1,
//     //   pageSize: -1,
//     // };
//     // getOrgUserList(para).then((res) => {
//     //   const data: any = res?.data;
//     //   const _Data: any = data?.rows;
//     //   dict.routerUser[routeId] = _Data || [];
//     //   // getUsefilter(levelCode);
//     // });
//     // // } else {
//     return routeId;
//     // }
//   }
// }

/**
 * 压缩图片
 */
export function compressFile(file: any) {
  return new Promise((resolve, reject) => {
    const options: any = {
      success(result: any) {
        // 将压缩后的 Blob 转换为 File 对象（如果组件支持Blob则不用这一步）
        const compressedFile = new File([result], file.name, {
          type: file.type,
          lastModified: Date.now(),
        });
        return resolve(compressedFile);
      },
      error(e: any) {
        return reject(e);
      },
    };
    // 1-3MB
    if (file.size > 1 * 1024 * 1024 && file.size <= 3 * 1024 * 1024) {
      options.quality = 0.3; // 压缩质量
      options.convertSize = false; //不进行图像尺寸的调整
      options.checkOrientation = false; // 图片翻转，默认为false
    }
    // 3-4MB
    if (file.size > 3 * 1024 * 1024 && file.size <= 4 * 1024 * 1024) {
      options.quality = 0.25; // 压缩质量
      options.convertSize = false; //不进行图像尺寸的调整
      options.checkOrientation = false; // 图片翻转，默认为false
    }
    // 5-6MB
    if (file.size > 5 * 1024 * 1024 && file.size <= 6 * 1024 * 1024) {
      options.quality = 0.2; // 压缩质量
      options.convertSize = false; //不进行图像尺寸的调整
      options.checkOrientation = false; // 图片翻转，默认为false
    }
    // 6-7MB
    if (file.size > 6 * 1024 * 1024 && file.size <= 7 * 1024 * 1024) {
      options.quality = 0.15; // 压缩质量
      options.convertSize = false; //不进行图像尺寸的调整
      options.checkOrientation = false; // 图片翻转，默认为false
    }
    // 7-9MB
    if (file.size > 7 * 1024 * 1024 && file.size <= 9 * 1024 * 1024) {
      options.quality = 0.1; // 压缩质量
      options.convertSize = false; //不进行图像尺寸的调整
      options.checkOrientation = false; // 图片翻转，默认为false
    }
    new ImageCompressor(file, options);
  });
}

export const formatImage = (item: string) => {
  if (!item) return '--';
  const format = ['.jpg', '.png', '.jpeg', '.gif', '.bmp', '.jfif'];
  let url = '';
  //截取后缀名拼接在后缀名前面拼接_80X80
  if (format.includes(item.substring(item.lastIndexOf('.')))) {
    url = item.substring(0, item.lastIndexOf('.')) + '_80X80' + item.substring(item.lastIndexOf('.'));
  }
  return url;
};

export function normalizeAddress(item: any) {
  let address =
    (item.buildingName || item.buildName || '') +
    '' +
    (item.floorName || item.floorName || '') +
    '' +
    (item.deviceAddress || item.faultAddress || '');

  if (item.unitType != 0 && item.unitType) {
    address = (item.houseNumber || '') + (item.deviceAddress || '');
  }

  return address.trim() === '' ? '未采集' : address;
}
