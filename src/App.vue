<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 14:21:35
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-25 14:27:43
 * @FilePath: \jx-paiming\exam-rank\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-config-provider
    preflight-style-disabled
    :locale="zhCN"
    :date-locale="dateZhCN"
    :theme="isDark ? darkTheme : undefined"
    :theme-overrides="isDark ? theme.dark(primaryColor) : theme.light(primaryColor)"
    :component-options="componentOptions"
  >
    <n-dialog-provider>
      <router-view />
    </n-dialog-provider>
  </n-config-provider>
</template>

<script lang="ts" setup>
import {
  NConfigProvider,
  zhCN,
  dateZhCN,
  NSelect,
  NCascader,
  NDataTable,
  darkTheme,
  GlobalComponentConfig,
  NDialogProvider,
} from 'naive-ui';

import '@/css/nUiAdapt/index.scss';
import { themeStore } from '@/theme/ThemeStore';
import { naiveThemeOverrides } from '@/theme';
import EmptyComp from '@/components/empty/Empty.vue';
import { useCssVar } from '@vueuse/core';
import { computed, h, ref } from 'vue';
const theme = naiveThemeOverrides();
const componentOptions = ref<GlobalComponentConfig>({
  Empty: {
    renderIcon: () => h(EmptyComp),
    description: '暂无数据',
  },
});

const primaryColor = useCssVar('--skin-c1', document.documentElement);
const isDark = computed(() => themeStore.themeRef.value.theme === 'dark');

// 全局修改NaiveUI组件默认属性
NSelect.props.consistentMenuWidth = { type: Boolean, default: false };
NCascader.props.virtualScroll = { type: Boolean, default: false };
NDataTable.props.striped = { type: Boolean, default: true };
</script>

<style scoped lang="scss"></style>
