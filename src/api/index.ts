/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 20:51:28
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-25 09:35:42
 * @FilePath: \jx-paiming\exam-rank\src\api\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import demo from './demo.ts';

export const api = {
  type: {
    exam: 'ehs-exam-rank-service', //安全排名服务
    risk: 'ehs-clnt-rmc-service',
  },
  name: merge(demo),
  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    const url = window.$SYS_CFG.apiBaseURL;
    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName?.indexOf('/') === 0 ? apiName : '/' + apiName;
    const _serviceType = serviceType ? '/' + serviceType : '';

    return `${url}${_serviceType}${_apiName}${paramsStr}`;
  },
};
