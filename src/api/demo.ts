/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-23 10:03:44
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-25 09:46:08
 * @FilePath: \jx-paiming\exam-rank\src\api\demo.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  demo: {
    login: '/auth/login', // 系统登录
    logout: '/auth/logout', // 退出登录
    uploadfile: '/fileUpload/fileUpload', // 文件上传
    queryDictDataByType: '/dict/queryDictDataByType', // 字典
    getDictDataTree: '/dict/getDictDataTree', // 字典树
    deptTree: '/dept/v1/deptTree', // 部门树

    /* demo模块 */
    queryInspectPageList: '/demo/v1/queryPageList', // 分页
    queryInspectInfoById: '/demo/v1/queryInspectInfoById', // 详情
    saveAndUpdateInspect: '/demo/v1/saveAndUpdate', // 保存/新增
    deleteInspectInfoById: '/demo/v1/deleteInspectInfoById', // 删除
    placeInspectImport: '/demo/v1/import', // 导入
    placeInspectExport: '/demo/v1/export', // 导出
  },
};
