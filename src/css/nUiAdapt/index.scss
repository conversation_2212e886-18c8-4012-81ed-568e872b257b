// 因 N ui 样式为变量复制 手动写scss 文件令其 可被postcss-px-to-viewport 转化为vw 适应 仅在所需页面引入

.adapt {
  .n-button {
    --n-font-size: 14px !important;
    --n-height: 34px !important;
    --n-padding: 0 14px !important;
  }
  .n-input{
    --n-font-size: 14px !important;
    --n-height: 34px !important;
    --n-padding: 0 12px !important;
  }
  .n-form-item .n-form-item-label {
    --n-label-padding: 0 12px 0 0 !important;
    --n-label-font-size: 14px !important;
  }
}



// .n-input .n-input__input-el {
//   --n-height: 34px!important;
// }
// .n-form-item.n-form-item--left-labelled .n-form-item-label{
//   --n-blank-height: 34px !important;
// }
// .n-form-item .n-form-item-blank{
//       --n-blank-height: 34px !important;
// }
// .n-collapse .n-collapse-item{
//       --n-font-size: 14px !important;
// }
// .n-collapse .n-collapse-item .n-collapse-item__header{
//   --n-font-size: 14px !important;
// }
// .n-base-selection{
//   --n-height: 34px!important;
//     --n-font-size: 14px !important;
// }
// .n-input{
//   --n-font-size: 14px !important;
// }
// .n-base-selection .n-base-selection-label .n-base-selection-input{
//   padding: 0 26px 0 12px;
// }
// .n-collapse .n-collapse-item .n-collapse-item__header .n-collapse-item-arrow{
//   font-size: 18px;
// }

// .n-base-selection-label {
//   --n-height: 34px !important;
// }