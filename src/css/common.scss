/**
 * 公共类
 */

body {
  font-size: 14px;
  min-width: 1280px;
}

@mixin autofill($color, $theme) {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-background-clip: text;
    -webkit-text-fill-color: $color;
    color-scheme: $theme;
  }
}

.com-autofill-dark-none {
  @include autofill(rgba(255, 255, 255, 0.9), dark);
}

.com-autofill-light-none {
  @include autofill(rgba(0, 0, 0, 0.9), light);
}

/* com-g-x-x -> */

.com-g-row-aa {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-a1 {
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-aa1 {
  display: grid;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-full {
  grid-template-rows: 100%;
}

.com-g-col-a1 {
  display: grid;
  grid-template-columns: auto 1fr;
}

.com-g-col-1a {
  display: grid;
  grid-template-columns: 1fr auto;
}

.com-g-col-full {
  grid-template-columns: 100%;
}

/* com-g-x-x  <- */

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #bbccf3 !important;
}

:deep(.n-data-table .n-data-table-td) {
  background-color: #eef7ff !important;
}

:deep(
  .n-data-table .n-data-table-tr.n-data-table-tr--striped .n-data-table-td
) {
  background: rgba(223, 238, 252, 0.99) !important;
}

//   gis图例
.storeyNum {
  min-width: 38px;
  // z-index: 999;

  .iconTop {
    height: 38px;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    cursor: pointer;

    i {
      border-bottom: 10px solid #d9d9d9;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      height: 0;
      width: 0;
    }
  }

  .floorTop {
    min-width: 38px;
    min-height: 38px;
    max-height: 190px;
    background: #fff;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;
    border-radius: 4px;

    li {
      padding: 0 5px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      white-space: nowrap;
      cursor: pointer;
    }

    .floorbarActived {
      color: #ffffff;
      background: linear-gradient(169deg, #16436f 21%, #1d9df2 86%);
      outline: 0;
      border-radius: 4px;
    }
  }

  .floorTop::-webkit-scrollbar {
    display: none;
  }

  .iconBottom {
    height: 38px;
    border-radius: 0 0 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    cursor: pointer;

    i {
      border-top: 10px solid #d9d9d9;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      height: 0;
      width: 0;
    }
  }
}

.ellipsis_o {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

// 组织树拉伸图标样式
.SwapHorizontalIcon-bg {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  z-index: 999;
}
// 自定义分页位置
.selfPaginationXY {
  position: absolute;
  right: 1.25vw;
  bottom: 1.25vw;
  // right:2.5vw;
  // bottom: 2.5vw;
  background-color: #eef7ff;
}
/* 定制滚动条整体 */
::-webkit-scrollbar {
  width: 6px;
  /* 宽度 */
}

/* 定制滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
  /* 轨道颜色 */
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.2);
}
/* 滑块在鼠标悬停时改变颜色 */
//   :hover::-webkit-scrollbar-thumb {
//     border-radius: 10px;
//     -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
//     background-color: rgba(0, 0, 0, 0.2);
//   }
