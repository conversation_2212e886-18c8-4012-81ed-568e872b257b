/*
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-11 10:19:57
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-10-25 16:46:56
 * @FilePath: \ehs-risk-mgr\src\store\type.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>;
  dictOption: Partial<DictType> | any;
  query: Partial<queryType>;
  routerUser: Partial<routeType> | any;
  gisStore: Partial<mapGis>;
  isExpand: boolean;
  selectUnitID: string;
  collapsedFlag: boolean;
  collapsedTree: any;
  jgDrawer: any;
}

/**
 * 登录接口返回的信息 todo
 */
export interface ILoginRes {
  id: string;
  userName: string;
  loginName: string;
  userTelphone: string;
  photoUrl: string;
  userSex: string;
  password: string;
  expireTime: string | number;
  uid: string;
  token: string;
  sysCode: string;
  querySysCode: string;
  queryToken: string;
  postId: string;
  postName: string;
  orgCode: string;
  orgName: string;
  deptId: string;
  deptName: string;
  unitId: string;
  unitName: string;
  userType: string;
  resourceVoList: Array<resourceRes>;
  roleIds: string;
  zhId: string;
  zhName: string;
  zhLogo: string;
  zhLogoUrl: string;
  logoPicUrl: string;
  iconPicUrl: string;
}

export interface DictState {
  /** option */
  dictOption: Partial<DictType> | any;
}

export interface queryType {
  querySysCode: string;
  queryToken: string;
}

export interface mapGis {
  openModel: boolean;
  openTreeModel: boolean;
  openRiskModel: boolean;
  objectId: any;
  riskObj: any;
  aerialMapType: any;
  areaGis: any;
  areaGisChange: any;
  gridIds: any;
  completelyFlag: any;
  gridNo: any;
  riskColor: any;
}

export interface DictType {
  /** option */
  fxdType: Array<DictRes>;
  pgffType: Array<DictRes>;
  xzffType: Array<DictRes>;
  bsztType: Array<DictRes>;
  gyfxType: Array<DictRes>;
  fxdjType: Array<DictRes>;
  fxcztType: Array<DictRes>;
}

export interface routeType {
  levelArray: Array<leverRes>;
}
/**
 * 字典接口返回的信息
 */
export interface DictRes {
  id: string | number;
  unitId: string;
  zhId: string;
  dictType: string;
  dictLabel: string;
  dictCode: string;
  dictValue: string;
  dictIcon: string;
  sortNum: string | number;
  status: string | number;
  remark: string;
  createUserId: string;
  createTime: string;
  updateUserId: string;
  updateTime: string;
}

export interface leverRes {
  id: string | number;
  unitId: string;
  zhId: string;
  riskLevel: string;
  riskLevelName: string;
  riskControlLevelId: string;
  riskControlLevelName: string;
  responsibleText: string;
  responsibleJson: string;
  remark: string;
  createUserId: string;
  createTime: string;
  updateUserId: string;
  updateTime: string;
}

export interface resourceRes {
  id: string | number;
  parentId: string;
  resOrder: string | number;
  resName: string | number;
  resAlias: string | number;
  resUrl: string | number;
  resType: string | number;
  resIdent: string | number;
  resRequestType: string | number;
  resIcon: string | number;
  sysCode: string | number;
  dataRes: string | number;
  createTime: string | number;
  createUserId: string | number;
  updateTime: string | number;
  updateUserId: string | number;
  isVisibled: string | number;
  isAdmin: string | number;
  delFlag: string | number;
  isChoose: string | number;
  childrens: Array<resourceRes>;
}
