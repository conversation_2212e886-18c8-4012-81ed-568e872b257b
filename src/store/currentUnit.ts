/*
 * @Author: 悦悦 <EMAIL>
 * @Date: 2024-10-18 10:56:14
 * @LastEditors: 悦悦 <EMAIL>
 * @LastEditTime: 2024-10-24 12:16:27
 * @FilePath: /隐患一张图/ehs-hazard/src/store/currentUnit.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';

export default defineStore('currentUnit', {
  state: () => ({
    misOrgcode: '',
    currentUnitId: '',
    currentOrgcode: '',
    currentOrgName: '',
  }),

  actions: {},
});
