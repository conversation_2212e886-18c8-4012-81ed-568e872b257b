/*
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-11 10:19:57
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-10-26 13:30:37
 * @FilePath: \ehs-risk-mgr\src\store\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';
import pkg from '../../package.json';
import type { IAuthState } from './type';

export const useStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): IAuthState => ({
    collapsedFlag: true, //组织树伸缩
    collapsedTree: {
      defaultSize: '323px',
      size: '323px',
    }, //组织树伸缩信息
    selectUnitID: '', // 大屏下拉的id
    isExpand: false, //侧边栏伸缩
    /*
     1、userInfo: 用户信息
    */
    userInfo: {
      id: '110',
      userName: '管理员',
      loginName: 'admin',
      userTelphone: '',
      photoUrl: '',
      userSex: '',
      password: '',
      expireTime: 1727079381584,
      uid: '',
      token: '',
      sysCode: '',
      postId: '',
      postName: '',
      orgCode: '',
      orgName: '',
      deptId: '',
      deptName: '',
      unitId: '',
      unitName: '',
      userType: '',
      resourceVoList: [],
      roleIds: '',
      zhId: '',
    },
    /*
     1、 dictOption: 字典信息
     2、 fxdType     风险点类型
     3、 pgffType    评估方法
     4、 xzffType    固有风险、现状风险
     5、 bsztType    辨识状态
     6、 gyfxType    固有风险、现状风险
     7、 fxdjType    风险等级
     8、 fxcztType   风险告知卡状态
     9、 gkzhType    管控状态
    */
    dictOption: {
      fxdType: [],
      pgffType: [],
      xzffType: [],
      bsztType: [],
      gyfxType: [],
      fxdjType: [],
      fxcztType: [],
      gkzhType: [],
    },
    /*
     1、querySysCode: 外部系统给的sysCode、queryToken：外部系统给的token
    */
    query: {
      querySysCode: '',
      queryToken: '',
    },
    /*
     1、levelArray 获取配置的风险等级
    */
    routerUser: {
      levelArray: [],
    },
    /*
     1、gisStore -- fww
    */
    gisStore: {
      aerialMapType: -2, // 鸟瞰还是白膜
      openModel: false, //四色图分布-点击楼栋-楼层平面弹窗
      openTreeModel: false,
      openRiskModel: false, //四色图分布-点击楼层-关联风险点弹窗
      objectId: '', // 房间id
      gridNo: '', // 区域id
      // 待划分风险点信息 id用于区分待划分进入还是楼栋进入
      riskObj: {
        id: '',
        riskPointName: '',
        riskLevel: 0,
        locationBuildingId: '',
        locationFloorId: '',
      },
      // 正在操作的区域
      areaGis: [],
      areaGisChange: false,
      // 正在操作-新增的网格id
      gridIds: [],
      // 查询该楼栋下打开的楼层是否需要渲染整层
      completelyFlag: {
        buildingName: '',
        riskLevel: '',
        riskPointName: '',
        completelyRiskPointFlag: false,
      },
      // 彩蛋弹窗
      riskColor: {
        name: '',
        riskLv: '',
      },
    },
    /*
     监管弹窗
    */
    jgDrawer: {
      open: false,
      item: {},
      isDevice: false, // 如果需要只查看设备, 判断弹窗里只有设备信息
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.token);
    },
  },
  actions: {
    reset() {
      this.$reset();
    },
  },
});
