<template>
  <div class="w-full h-full flex flex-col">
    <div class="py-[10px]">
      <div class="mb-[10px]">按照各厂考核扣分情况，请填报扣分的分数，最终以扣分总数排名，扣分较少的排名考前</div>
    </div>
    <Filter @action="actionFn" ref="filterRef" :recordInfo="recordInfo" />
    <div class="flex-1 w-full h-full overflow-hidden">
      <div class="w-full h-full">
        <TableList ref="tableCompRef" @workflowrefresh="Getworkflowrefresh"></TableList>
      </div>
    </div>

    <div>
      <p class="font-bold">扣分标准：</p>
      <p class="text-[#666]">
        1.当月发现重大隐患（典型问题）且整改完毕，每项扣0.5分，未按时整改完毕每项次月翻倍扣分；<br />
        2.三管三必须落实、一为主四确认四参与、双重预防机制运行、小班组大安全落实等重点工作，每少完成
        1项，扣0.5分（未开展）；任务未按要求落实，每项扣0.2分；<br />
        3.回头看不达标，取消达标资格，每站扣0.5分，双达标当月考核排名最后扣0.5分，厂级自查不严扣1分；<br />
        4.按基础管理提升（以企业管理部的月度考核绩效为基础作为扣分执行）每月考核实际扣分执行 ；<br />
        5.日督查中各单位平均每条隐患扣0.01分，重大隐患与典型问题除外（平均隐患条数 = 隐患总数 / 现场总数）;<br />
        6.平台运行使用排名最后一名的扣0.5分。
      </p>
    </div>
    <!-- <div class="flex items-center mt-[15px]">
      下发通知公告人员：
      <div class="flex gap-[10px]">
        <n-tag type="info" v-for="n in userList" :key="n.id" closable @close="handleTagClose">
          {{ n.name }}
        </n-tag>
      </div>
    </div> -->
  </div>
</template>
<script setup lang="ts">
import { ACTION } from '@/views/demo-table/constant';
import { ref, onMounted } from 'vue';
import Filter from './Filter.vue';
import TableList from '@/views/home/<USER>/Table.vue';
import { nextTick } from 'process';
import { dayjs } from '@/utils/dayjs';
import { downloadFile } from '@/utils/dictData';

const filterRef = ref<any>(null);
const filterVal = ref<any>();

// const userList = ref([
//   {
//     name: '哪里都是你',
//     id: '11',
//   },
//   {
//     name: 'lisi',
//     id: '22',
//   },
// ]);

const tableCompRef = ref<any>();
const actionFn = ({ action, data }: { action: any; data: any }) => {
  filterVal.value = {
    ...filterRef.value?.getVal(),
  };
  if (action === ACTION.SEARCH) {
    // 搜索
    console.log('filterVal.value-=-=-=-=-=-', filterVal.value);
    tableCompRef.value.getTableDataWrap(filterVal.value);
  } else if (action === ACTION.EXPORT) {
    // 导出
    console.log('filterVal.value-=-=-=-=-=-', filterVal.value);
    try {
      downloadFile(filterVal.value, '/ranking/export');
    } catch (error) {
      console.log('error', error);
    }
  }
};
const recordInfo = ref({});
function Getworkflowrefresh(record: any, userlist: any) {
  console.log(userlist, '--=-=-=-=-=userlist');
  console.log('🚀 ~ Getworkflowrefresh ~ userlist:', userlist);
  recordInfo.value = JSON.parse(JSON.stringify(record));
  // shuserlist.value = JSON.parse(JSON.stringify(userlist));
}
onMounted(() => {
  // nextTick(() => {dayjs().subtract(1, 'month').format('YYYY-MM')
  tableCompRef.value.getTableDataWrap({
    date: dayjs(new Date()).format('YYYY-MM'),
    flag: 1,
  });
  // });
});

defineOptions({ name: 'HistoryIndex' });
</script>
