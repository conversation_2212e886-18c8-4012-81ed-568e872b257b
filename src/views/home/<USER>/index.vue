<template>
  <div class="w-full h-full flex flex-col">
    <div class="py-[10px]">
      <div class="mb-[10px]">按照各厂考核扣分情况，请填报扣分的分数，最终以扣分总数排名，扣分较少的排名考前</div>
      <div class="flex justify-between">
        <div>
          状态：<span class="text-[#DBA000] font-bold">{{ getRankingStatus(recordInfo.rankingType) }} </span>
          <div v-if="recordInfo.rankingType === '2'">
            退回原因：<span class="text-[#DBA000] font-bold">{{ recordInfo.rankingRemark || '--' }} </span>
          </div>
        </div>
        <div class="">
          <n-button type="primary" @click="handleExport"> 导出排名 </n-button>
        </div>
      </div>
    </div>
    <div class="flex-1 w-full h-full overflow-hidden">
      <div class="w-full h-full">
        <TableList ref="tableCompRef" @workflowrefresh="Getworkflowrefresh"></TableList>
        <!-- <n-data-table class="h-full" :columns="columns" :data="data" :pagination="pagination" :bordered="false" /> -->
      </div>
    </div>

    <div>
      <p class="font-bold">扣分标准：</p>
      <p class="text-[#666]">
        1.当月发现重大隐患（典型问题）且整改完毕，每项扣0.5分，未按时整改完毕每项次月翻倍扣分；<br />
        2.三管三必须落实、一为主四确认四参与、双重预防机制运行、小班组大安全落实等重点工作，每少完成
        1项，扣0.5分（未开展）；任务未按要求落实，每项扣0.2分；<br />
        3.回头看不达标，取消达标资格，每站扣0.5分，双达标当月考核排名最后扣0.5分，厂级自查不严扣1分；<br />
        4.按基础管理提升（以企业管理部的月度考核绩效为基础作为扣分执行）每月考核实际扣分执行 ；<br />
        5.日督查中各单位平均每条隐患扣0.01分，重大隐患与典型问题除外（平均隐患条数 = 隐患总数 / 现场总数）;<br />
        6.平台运行使用排名最后一名的扣0.5分。
      </p>
    </div>
    <!-- <div class="flex items-center mt-[15px]">
      下发通知公告人员：
      <div class="flex gap-[10px]">
        <n-tag type="primary" v-for="n in userList" :key="n.id" closable @close="handleTagClose">
          {{ n.name }}
        </n-tag>
        <n-button type="primary" size="small" @click="addUser">
          <n-icon size="20">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
              <path
                d="M368.5 240H272v-96.5c0-8.8-7.2-16-16-16s-16 7.2-16 16V240h-96.5c-8.8 0-16 7.2-16 16 0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7H240v96.5c0 4.4 1.8 8.4 4.7 11.3 2.9 2.9 6.9 4.7 11.3 4.7 8.8 0 16-7.2 16-16V272h96.5c8.8 0 16-7.2 16-16s-7.2-16-16-16z"
              />
            </svg> </n-icon
          >添加人员
        </n-button>
      </div>
    </div> -->

    <!-- 判断当前登录人是否是审核人 -->
    <div class="flex justify-center" v-if="recordInfo.rankingType == '1' || recordInfo.rankingType == '2'">
      <!-- <div class="mr-[8px]"><n-button type="primary"> 暂存 </n-button></div> -->
      <div class="ml-[8px]">
        <n-button type="primary" @click="showModal = true"> 提交审核 </n-button>
      </div>
    </div>
    <!-- 判断当前登录人是否是审核人  
      

      -->
    <div class="flex justify-center" v-if="recordInfo.rankingType == '3' && arrayContainsValuebyuser(shuserlist)">
      <div class="mr-[8px]">
        <n-button type="default" @click="returnshowModal = true"> 退回 </n-button>
      </div>
      <div class="ml-[8px]">
        <n-button type="primary" @click="handlePass"> 审核通过 </n-button>
      </div>
    </div>
  </div>
  <SubmitForReview
    :showModal="showModal"
    :recordId="recordInfo.recordId"
    @update:show="handleUpdateShow"
    @refresh="refresh"
  />
  <returnView
    :recordId="recordInfo.recordId"
    :showModal="returnshowModal"
    @update:show="handleReturnUpdateShow"
    @refresh="refresh"
  ></returnView>
</template>

<script setup lang="ts">
import SubmitForReview from '@/views/home/<USER>/submitForReview.vue';
import returnView from '@/views/home/<USER>/returnView.vue';
import { ref, reactive, h, onMounted } from 'vue';
import TableList from '@/views/home/<USER>/Table.vue';
import dayjs from 'dayjs';
import { useStore } from '@/store/index';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { downloadFile } from '@/utils/dictData';

const userInfo = useStore()?.userInfo;
function arrayContainsValuebyuser(arr: any) {
  if (arr?.length > 0 && arr !== undefined) {
    return arr.some((item: any) => item == useStore()?.userInfo.id);
  } else {
    return false;
  }
}

const tableCompRef = ref();
const showModal = ref(false);
const returnshowModal = ref(false);
const showSummaryModal = ref(false);
const handleUpdateShow = (value: boolean) => {
  showModal.value = value;
};
const handleReturnUpdateShow = (value: boolean) => {
  returnshowModal.value = value;
};
function handleExport() {
  // console.log(query);
  const query = {
    date: dayjs().subtract(0, 'month').format('YYYY-MM'),
    flag: 0,
  };
  downloadFile(query, '/ranking/export');
}
const radioValue = ref('1');
const songs = ref([
  {
    value: '1',
    label: dayjs().subtract(0, 'month').format('YYYY年MM月') + '排名',
  },
  {
    value: '2',
    label: '历史记录',
  },
]);
const recordInfo = ref<any>({});
const shuserlist = ref([]);
function Getworkflowrefresh(record: any, userlist: any) {
  // console.log('🚀 ~ Getworkflowrefresh ~ userlist:', userlist);
  recordInfo.value = JSON.parse(JSON.stringify(record));
  shuserlist.value = recordInfo.value.optUserId && recordInfo.value.optUserId.split(',');
  // console.log(shuserlist.value, '--=-=-=-=-=userlist');
}
function getRankingStatus(type: any) {
  return type == 1
    ? '待提报'
    : type == 2
      ? '退回待提报'
      : type == 3
        ? '待审核'
        : type == 4
          ? '暂存'
          : type == 5
            ? '已审核'
            : '--';
}
const handleTagClose = () => {
  console.log('🚀 ~ handleTagClose ~ handleTagClose:', handleTagClose);
};

const handlePass = async () => {
  console.log('🚀 ~ handlePass ~ recordInfo:', recordInfo);

  const url = api.getUrl(api.type.exam, '/ranking/updateRankingStatus');
  const res: any = await $http.post(url, {
    data: {
      rankingRecordId: recordInfo.value.recordId,
      // 节点类型 1:待提报 2:退回待提报 3:待审核 4:暂存 5:已审核
      rankingType: 5,
      _cfg: { showTip: true, showOkTip: true },
    },
  });

  refresh();
  console.log('🚀 ~ handlePass ~ res:', res);
};
const refresh = () => {
  init();
};
function init() {
  tableCompRef.value.getTableDataWrap({
    date: dayjs().subtract(0, 'month').format('YYYY-MM'),
    flag: 0,
  });
}
onMounted(() => {
  init();
});
defineOptions({ name: 'rankingIndex' });
</script>

<style scoped lang="scss"></style>
