<template>
  <n-drawer v-model:show="showModal" block-scroll :width="1100" :placement="placement" @update:show="handleUpdateShow">
    <n-drawer-content>
      <template #header>
        <!-- <div class="flex items-center gap-[12px]">
          <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
          汇总查看
        </div> -->
        <div class="flex items-center justify-between gap-[6px]">
          <div class="flex items-center">
            <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
            <span class="text-[18px] ml-[8px]">{{ unitName || '汇总查看' }}</span>
          </div>
          <div @click="handleUpdateShow(false)">
            <img src="@/assets/images/close.png" alt="" class="w-[50px]" />
          </div>
        </div>
      </template>
      <div class="h-full flex flex-col overflow-hidden">
        <div class="font-bold flex-shrink-0">
          <div class="mb-[8px]">扣分项明细 共{{ totalNum || 0 }}项</div>
        </div>
        <div class="flex-1 overflow-auto">
          <div class="flex flex-col" v-for="item in summaryDetailList" :key="item.itemTypeName">
            <div>
              <div class="mb-[8px]">{{ item.itemTypeName }}</div>
            </div>
            <div class="flex-1">
              <n-data-table
                :columns="columns"
                :data="item.rankingDeductionItems"
                :bordered="false"
                :render-cell="renderCell"
                max-height="calc(100%)"
              />
            </div>
          </div>
        </div>

        <div v-if="!summaryDetailList?.length" class="h-full">
          <div v-if="loading" class="h-full w-full flex items-center justify-center">
            <n-space>
              <n-spin size="large">
                <template #description>
                  <span class="text-[#2080f0]">加载中...</span>
                </template>
              </n-spin>
            </n-space>
          </div>

          <EmptyComp v-else :text="'暂无数据'" />
        </div>
      </div>
      <template #footer>
        <div class="w-full">
          <div class="footer flex justify-end items-center gap-[12px]">
            <div class="text-[#333] font-bold">当前合计扣分 {{ total }}分</div>
            <n-button type="primary" @click="handleUpdateShow(false)">确定</n-button>
          </div>
        </div>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>
<script lang="ts" setup>
import { computed, ref, h, reactive, onMounted, watch } from 'vue';
import { NButton, NText, NTooltip } from 'naive-ui';
import EmptyComp from '@/components/empty/Empty.vue';

import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import Tootip from './tootip.vue';

const props = defineProps({
  showModal: Boolean,
  recordId: String,
  unitName: String,
});
const emits = defineEmits(['update:show']);
const showModal = computed(() => props.showModal);
const placement = ref('right');
const loading = ref(false);

const handleUpdateShow = (value: boolean) => {
  emits('update:show', value);
};
const renderCell = (value: any) => {
  if (!value) return h(NText, { depth: 3 }, { default: () => '--' });
  return value;
};

const pagination = reactive({
  page: 2,
  pageSize: 5,
  showSizePicker: true,
  pageSizes: [3, 5, 7],
  total: 1888,
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});

// 根据ID获取汇总查看
const summaryDetailList = ref<any[]>([]);
const total = ref('');
const totalNum = ref(0);
const summaryDetail = async (recordId: string) => {
  loading.value = true;
  const url = api.getUrl(api.type.exam, '/ranking/summaryDetail', {
    summaryDetailId: recordId,
    _cfg: { showTip: true, showOkTip: true },
  });
  try {
    const res: any = await $http.post(url);
    console.log('🚀 ~ summaryDetail ~ res:', res);
    summaryDetailList.value = res?.data?.summaryDetailList || [];
    total.value = res?.data?.total || '';
    totalNum.value = res?.data?.totalNum || 0;
  } catch (error) {
    console.log('🚀 ~ summaryDetail ~ error:', error);
  } finally {
    loading.value = false;
  }
};
watch(
  () => props.showModal,
  (val) => {
    if (val) {
      console.log('🚀 ~ props.recordId:', props.recordId);
      totalNum.value = 0;
      total.value = '0';
      summaryDetailList.value = [];
      summaryDetail(props.recordId || '');
    }
  }
);

const columns = ref([
  {
    title: '扣分类型',
    key: 'itemSourceType',
    width: 100,
    render(row: any) {
      if (row.itemSourceType == 1) {
        return h('NText', '系统自动');
      } else {
        return h('NText', '人工扣分');
      }
    },
  },
  {
    title: '扣分原因',
    key: 'itemReason',
    ellipsis: {
      tooltip: true,
    },
    render(row: any) {
      return row.deductionExplanation + row.itemReason;
    },
  },
  {
    title: '依据项',
    key: 'itemBase',
    render(row: any) {
      if (row.itemSourceType != 2) {
        return h('NText', row.itemBase || '--');
      } else {
        if (!row.files?.length) return h('NText', '--');
        //       <n-tooltip trigger="hover">
        //   <template #trigger>
        //     <n-button> 鸭子 </n-button>
        //   </template>
        //   如果它长得像鸭子，走起来像鸭子，叫起来也像鸭子，那它一定是个鸭子。
        // </n-tooltip>
        return h(
          'div',
          {
            style: {
              width: '100%',
            },
          },
          [
            row.files.map((i: any) => {
              return h(
                Tootip,
                {
                  item: i,
                },
                {
                  default: () => i.fileName || '--',
                }
              );
            }),
          ]
        );
        // return h(
        //   'div',
        //   {
        //     style: {
        //       color: '#2080f0',
        //       cursor: 'pointer',
        //     },
        //     onClick: () => openFile(row, '2'),
        //   },
        //   {
        //     default: () => row.itemBase || '--',
        //   }
        // );
      }
    },
  },
  {
    title: '依据单位',
    key: 'deductionUnitName',
  },
  {
    title: '扣分',
    key: 'deductionValue',
    width: 100,
  },
  {
    title: '扣分人',
    key: 'deductionUserName',
    width: 100,
  },
]);

onMounted(() => {
  // summaryDetail("1");
});

defineExpose({
  handleUpdateShow,
});
</script>

<style lang="scss" scoped>
:deep(.n-drawer-header) {
  padding: 8px 16px !important;
}
</style>
