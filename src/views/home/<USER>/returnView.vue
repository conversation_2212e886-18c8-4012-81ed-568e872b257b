<!-- 退回 -->
<template>
  <n-modal v-model:show="showModal" @update:show="handleUpdateShow">
    <div>
      <div class="w-[800px] min-h-[500px] bg-[#fff] flex-shrink-0 flex flex-col">
        <div class="flex items-center justify-between gap-[6px] border-b border-[#ccc]">
          <div class="flex items-center gap-[12px] p-[16px] font-bold">
            <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
            审核退回
          </div>
          <div @click="handleUpdateShow(false)">
            <img src="@/assets/images/close.png" alt="" class="w-[50px]" />
          </div>
        </div>

        <div class="flex-1 p-[20px]">
          <div class="">
            <div class="py-[10px]">退回说明</div>
            <n-input v-model:value="rankingRemark" type="textarea" maxlength="100" placeholder="请输入备注说明" />
          </div>
        </div>
        <div class="footer flex justify-end gap-[12px] p-[20px]">
          <n-button @click="handleUpdateShow(false)">取消</n-button>
          <n-button type="primary" @click="handleSubmit">提交</n-button>
        </div>
      </div>
    </div>
  </n-modal>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';

import { api } from '@/api';
import { $http } from '@tanzerfe/http';

const props = defineProps({
  showModal: Boolean,
  recordId: String,
});
const emits = defineEmits(['update:show', 'refresh']);
const showModal = computed(() => props.showModal);
const openModal = () => {
  emits('update:show', true);
};
const handleUpdateShow = (value: boolean) => {
  emits('update:show', value);
};

const options = ref([
  {
    value: '01',
    label: '张三',
  },
  {
    value: '02',
    label: '李四',
  },
]);
const loading = ref(false);
const rankingRemark = ref('');
// /ranking/updateRankingStatus
const updateRankingStatus = async (recordId: string) => {
  const url = api.getUrl(api.type.exam, '/ranking/updateRankingStatus');
  const res: any = await $http.post(url, {
    data: {
      rankingRecordId: recordId,
      rankingRemark: rankingRemark.value,
      // 节点类型 1:待提报 2:退回待提报 3:待审核 4:暂存 5:已审核
      rankingType: '2',
    },
  });
  console.log('🚀 ~ summaryDetail ~ res:', res);
  emits('refresh');
  handleUpdateShow(false);
};

const handleSubmit = () => {
  updateRankingStatus(props.recordId || '');
};
defineOptions({ name: 'SubmitForReview' });
</script>
<style scoped lang="scss"></style>
