<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 10:13:18
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-26 16:17:38
 * @FilePath: \jx-paiming\exam-rank\src\views\home\comp\aside\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <ComDrawerA :autoFocus="false" :footerPaddingBottom="25" :maskClosable="false" class="!w-[550px]">
    <div :class="$style.wrap">
      <Add ref="addRef" @submitted="handleSubmitted" :handleClose="handleClose" />
    </div>
  </ComDrawerA> -->
  <n-drawer v-model:show="showModal" block-scroll :width="450" :placement="placement" @update:show="handleClose">
    <n-drawer-content>
      <template #header>
        <div class="flex items-center justify-between gap-[6px]">
          <div class="flex items-center">
            <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
            <span class="text-[18px]">{{ title }}</span>
          </div>
          <div @click="handleClose">
            <img src="@/assets/images/close.png" alt="" class="w-[50px]" />
          </div>
        </div>
      </template>
      <div class="h-full flex flex-col">
        <div style="font-weight: 600; font-size: 16px">扣分项明细 共{{ unTotal }}项</div>
        <div class="flex-1 overflow-auto" v-if="tableData.length > 0">
          <div
            v-for="item in tableData"
            :key="item.id"
            class="bg-[#ffffff] border border-[#eaeaea] rounded-[8px] mb-[12px] p-[12px] border-l-[6px] border-l-[#3e62eb] flex flex-col"
          >
            <div style="display: flex; justify-content: space-between">
              <div>
                [{{ item.itemSourceType === '1' ? '系统自动' : '人工扣分' }}]&nbsp;<span style="color: red"
                  >-{{ item.deductionValue }}</span
                >
              </div>
              <div v-if="item.itemSourceType === '2'">扣分人：{{ item.deductionUserName }}</div>
              <div v-if="item.type === '2'">扣分人：{{ item.deductionUserName }}</div>
            </div>
            <div v-if="item.itemSourceType === '1'">
              <span style="font-size: 15px; font-weight: 600">{{ item.deductionExplanation }}</span
              >&nbsp;<span style="font-size: 14px; color: blue">{{ item.itemReason }}</span>
            </div>
            <div v-else style="font-weight: 600">{{ item.itemReason }}</div>
            <div>
              <p v-if="item.itemSourceType === '1'">
                依据项：<span style="color: blue" v-if="item.itemBase !== ''">{{ item.itemBase }}</span>
                <span v-else>--</span>
              </p>

              <div v-else>
                依据项：
                <p v-for="i in item.files" :key="i.id" style="color: blue; cursor: pointer" @click="view(i)">
                  {{ i.fileName }}
                </p>
              </div>
            </div>
            <div>
              <span>依据单位：</span><span>{{ item.deductionUnitName || '--' }}</span>
            </div>
            <div>
              <n-tag
                type="primary"
                style="margin-right: 5px; cursor: pointer"
                @click="edit(item)"
                v-if="item.itemSourceType === '2' && newObj.flag !== 1"
              >
                编辑
              </n-tag>
              <n-tag style="cursor: pointer" type="error" @click="handleDelete(item)" v-if="newObj.flag !== 1">
                删除
              </n-tag>
            </div>
          </div>
        </div>
        <EmptyComp v-else />
      </div>
      <template #footer>
        <div class="w-full">
          <div class="footer flex justify-between items-center gap-[12px]">
            <div>
              <n-button type="primary" @click="open" v-if="newObj.flag !== 1">
                <IconAdd />
                添加扣分项</n-button
              >
            </div>
            <div class="text-[#333] font-bold">
              <!-- 计算数据deductionValue的总和 -->
              <span class="text-[#333] font-bold"> 当前合计扣分 {{ totalSUM }}分</span>
              <n-button type="primary" style="margin-left: 5px" @click="submit()">确定</n-button>
            </div>
          </div>
        </div>
      </template>
    </n-drawer-content>
    <AddDemeritPoints
      ref="addDemerItPonitsRef"
      :showModal="showSummaryModal"
      :obj="newObj"
      :title="sonTitle"
      @update:show="handleCloseAdd"
    />
  </n-drawer>
</template>

<script lang="ts" setup>
import ComDrawerA from '@/components/drawer/ComDrawerA.vue';
import AddDemeritPoints from '@/views/home/<USER>/addDemeritPoints.vue';
import { $dialog } from '@/common/shareContext/useDialogCtx.ts';
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { deleteItem, checkDetail } from '../../fetchData.ts';
import { computed, inject, Ref, ref, onMounted, watch, onBeforeUnmount } from 'vue';
import { Base64 } from 'js-base64';
import EmptyComp from '@/components/empty/Empty.vue';
import Add from './Add.vue';
const props: any = defineProps({
  rowData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  title: {
    type: String,
    default: '',
  },
  isShowAside: {
    type: Boolean,
  },
});
const view = (obj: any) => {
  const type = obj.fileName.split('.');
  const typeName = type[type.length - 1];
  if (typeName === 'docx' || typeName === 'doc') {
    var b64Encoded = Base64.encode(getApiBase() + obj.fileUrl);
    window.open(window.$SYS_CFG.apiPdfPreviewURL + encodeURIComponent(b64Encoded));
  } else {
    window.open(getApiBase() + obj.fileUrl);
  }
};
const emits = defineEmits(['action', 'refresh']);
const addDemerItPonitsRef = ref(null);
const tableData = ref([]);

function getApiBase() {
  if (window.location.hostname == 'localhost') {
    return 'https://test-bw.gsafetycloud.com' + '/ehs';
  } else if (window.location.hostname == 'agjp.tanzervas.com') {
    return window.location.origin + '/aqsc/v1' + '/ycsy';
  } else {
    return window.location.origin;
  }
}
const submit = () => {
  emits('refresh');
  emits('update:show' as any, false);
};

const newObj = ref({});
const unTotal = ref(0);
const pageNum = ref(1);
const totalSUM = ref(''); //总分
const getTableData = async (obj: any, start: any) => {
  newObj.value = obj;
  if (!start) return;
  if (props.isShowAside === false) return;
  let res = await checkDetail({
    itemType: obj.type,
    orgCode: obj.orgCode,
    recordId: obj.id,
    pageNo: pageNum.value,
    pageSize: 100,
  });
  tableData.value.push(...res.data.rows);
  unTotal.value = res.data.total;
  if (tableData.value.length >= unTotal.value) {
    totalSUM.value = tableData.value.reduce((acc: any, cur: any) => acc + cur.deductionValue, 0);
  } else {
    pageNum.value += 1;
    totalSUM.value = '计算中...';
    getTableData(obj, true);
  }
};

const showSummaryModal = ref(false);
const open = () => {
  sonTitle.value = '添加';
  showSummaryModal.value = true;
  addDemerItPonitsRef.value.getUnitData(newObj.value.orgCode);
};
function handleDelete(row: any) {
  $dialog.error({
    title: '删除',
    content: '确定删除吗?',
    positiveText: '确定',
    negativeText: '取消',
    transformOrigin: 'center',
    closable: false,
    onPositiveClick: async () => {
      deleteItem({ id: row.id }).then((res) => {
        pageNum.value = 1;
        tableData.value = [];
        unTotal.value = 0;
        totalSUM.value = '计算中...';
        getTableData(newObj.value, true);
      });
    },
  });
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}
function handleCloseAdd(value: boolean) {
  console.log(newObj.value, '============newObj.value');
  showSummaryModal.value = value;
  pageNum.value = 1;
  tableData.value = [];
  unTotal.value = 0;
  getTableData(newObj.value, true);
}

const sonTitle = ref('');
const edit = (obj) => {
  sonTitle.value = '编辑';
  showSummaryModal.value = true;
  addDemerItPonitsRef.value.getDetailData(obj.id);
  addDemerItPonitsRef.value.getUnitData(newObj.value.orgCode);
};
const handleUpdateShow = (value: boolean) => {
  emits('update:show', value);
};
watch(
  () => props.isShowAside,
  (val) => {
    pageNum.value = 1;
    tableData.value = [];
    unTotal.value = 0;
    totalSUM.value = '计算中...';
    getTableData(newObj.value, props.isShowAside);
    if (!val) {
      emits('refresh');
    }
  },
  { immediate: true }
);
defineExpose({
  getTableData,
});

defineOptions({ name: 'HomeAside' });
</script>

<style lang="scss" scoped>
:deep(.n-drawer-header) {
  padding: 8px 16px !important;
}
.wrap {
  padding: 24px;
}
</style>
