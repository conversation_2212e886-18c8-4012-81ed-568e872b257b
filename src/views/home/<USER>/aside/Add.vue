<template>
  <div class="kou">
    <div class="header">扣分项明细&nbsp;共<span>3</span>项</div>
    <div class="content">
      <div v-for="item in tableData" :key="item.id" class="list">
        <div class="li">
          <div class="header">
            <div>
              [{{ item.type === '1' ? '系统自动' : '人工扣分' }}]&nbsp;<span>{{
                item.num
              }}</span>
            </div>
            <div v-if="item.type === '2'">扣分人：{{ item.person }}</div>
          </div>
          <div class="text">
            <div v-if="item.type === '1'">
              {{ item.name }}&nbsp;<span>{{ item.text }}</span>
            </div>
            <div v-else>【扣分原因】</div>
          </div>
          <div class="text1">
            <div v-if="item.type === '1'">
              依据项：<span>{{ item.yiju }}</span>
            </div>
            <div v-else>依据项：</div>
          </div>
          <div class="text2">
            <div>
              依据单位：<span>{{ item.unit }}</span>
            </div>
          </div>
          <div class="bot">
            <n-button
              type="primary"
              style="margin-right: 5px"
              v-if="item.type === '2'"
              >编辑</n-button
            >
            <n-button type="error">删除</n-button>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom">
      <div>
        <n-button type="warning" @click="open">
          <IconAdd />
          添加扣分项</n-button
        >
      </div>

      <div>
        <span>当前合计扣分：1.5分</span>&nbsp;<n-button
          type="primary"
          @click="handleClose"
          >确认</n-button
        >
      </div>
    </div>
  </div>
  <AddDemeritPoints
    :showModal="showSummaryModal"
    @update:show="showSummaryModal = false"
  />
</template>

<script setup lang="ts">
import { FlFilledAdd as IconAdd } from '@kalimahapps/vue-icons';
import { ref } from 'vue';
import AddDemeritPoints from '@/views/home/<USER>/addDemeritPoints.vue';
const showSummaryModal = ref(false);
const open = () => {
  showSummaryModal.value = true;
};
const tableData = ref([
  {
    id: 1,
    type: '1',
    person: '',
    name: '井场安全阀失效',
    text: '未整改完成',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',
    num: '-0.5',
  },
  {
    id: 2,
    type: '2',
    person: '姜子煜',
    name: '井场安全阀失效',
    text: '',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',

    num: '-0.5',
  },
  {
    id: 2,
    type: '2',
    person: '姜子煜',
    name: '井场安全阀失效',
    text: '',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',

    num: '-0.5',
  },
  {
    id: 2,
    type: '2',
    person: '姜子煜',
    name: '井场安全阀失效',
    text: '',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',

    num: '-0.5',
  },
  {
    id: 2,
    type: '2',
    person: '姜子煜',
    name: '井场安全阀失效',
    text: '',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',

    num: '-0.5',
  },
  {
    id: 2,
    type: '2',
    person: '姜子煜',
    name: '井场安全阀失效',
    text: '',
    yiju: '分类-【教育培训】',
    unit: '1号集气站',

    num: '-0.5',
  },
]);
defineOptions({ name: 'HomeAdd' });
</script>

<style scoped lang="scss">
.kou {
  width: 100%;

  .header {
    font-weight: 600;

    span {
      font-size: 12px;
    }
  }
  .content {
    width: 100%;

    .list {
      width: 100%;

      .li {
        width: 100%;
        padding-top: 10px;
        padding-left: 5px;
        padding-right: 5px;
        padding-bottom: 15px;
        border: 1px solid rgb(202, 200, 200);
        // background: red;
        margin-bottom: 10px;
        border-radius: 5px;
        .header {
          width: 100%;
          justify-content: space-between;
          display: flex;
          span {
            color: red;
            font-size: 16px;
          }
        }
        .text {
          font-size: 16px;
          span {
            color: blue;
            font-size: 14px;
          }
        }
        .text1 {
          span {
            color: blue;
            font-size: 14px;
          }
        }
        .text2 {
          span {
            font-size: 16px;
          }
        }
        .bot {
        }
      }
    }
  }
  // position: relative;
  .bottom {
    width: 93%;
    display: flex;
    position: fixed;
    justify-content: space-between;
    bottom: 24px;
    span {
      font-weight: 600;
      font-size: 16px;
    }
  }
}
</style>
