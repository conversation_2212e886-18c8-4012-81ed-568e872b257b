import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

// 获取生产排名列表
export function rankingListAPI(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/ranking/list', data);
  return $http.post(url, {
    data: { ...data },
  });
}
export function deleteItem(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/deductionItem/deleteItem', data);
  return $http.post(url, {
    data: { ...data },
  });
}
export function checkDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/deductionItem/checkDetail', data);
  return $http.post(url, {
    data: { ...data },
  });
}
export function getDetail(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/deductionItem/getDetail', data);
  return $http.post(url, {
    data: { ...data },
  });
}
export function saveOrUpdate(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/deductionItem/saveOrUpdate');
  return $http.post(url, {
    data: { ...data },
  });
}
export function getOrgCodeList(data: IObj<any>) {
  const url = api.getUrl(api.type.exam, '/deductionItem/getOrgTree', data);
  return $http.post(url, {
    data: { ...data },
  });
}
