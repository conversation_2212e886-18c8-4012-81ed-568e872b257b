<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 09:03:02
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-24 14:20:34
 * @FilePath: \jx-paiming\exam-rank\src\views\home\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 09:03:02
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-24 10:16:53
 * @FilePath: \jx-paiming\exam-rank\src\views\home\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <div class="grid place-content-center place-items-center">Home page111000</div> -->
  <div class="w-full h-full flex flex-col overflow-hidden">
    <com-bread :data="breadData"></com-bread>
    <div class="w-full h-full flex flex-col relative">
      <div class="w-full flex items-center translate-y-[5px]">
        <RadioTab :tabList="songs" :tab="radioValue" @change="radioValue = $event" />
        <!-- <div class="cursor-pointer px-[30px] py-[10px]" v-for="song in songs" :key="song.value"
            :class="{ 'bg-[rgb(52,152,219)] text-[#fff]': radioValue === song.value }" @click="radioValue = song.value">
            <div class="">
              <div>{{ song.label }}</div>
            </div>
          </div> -->
      </div>
      <div class="w-full h-full bg-[rgb(238,247,255)] p-[20px] rounded-[10px] flex flex-col flex-1 relative z-10">
        <div class="w-full h-full" v-if="radioValue === '1'">
          <ranking></ranking>
        </div>
        <div class="w-full h-full overflow-hidden flex-1" v-else>
          <History></History>
        </div>
      </div>

      <!-- <n-tabs type="line" animated>
        <n-tab-pane class="flex-1 h-full" name="1" :tab="Onelabel">1111</n-tab-pane>
        <n-tab-pane class="flex-1 h-full" name="2" tab="历史记录">
        
        </n-tab-pane>
      </n-tabs> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import RadioTab from '@/components/tabs/ComRadioTab.vue';

import { IBreadData } from '@/types/index';
import { ref } from 'vue';
import dayjs from 'dayjs';
import ranking from './ranking/index.vue';
import History from '../home/<USER>/index.vue';

const breadData: IBreadData[] = [{ name: '安全考核排名' }];

const radioValue = ref('1');
const songs = ref([
  {
    name: '1',
    label: dayjs().subtract(0, 'month').format('YYYY年MM月') + '排名',
  },
  {
    name: '2',
    label: '历史记录',
  },
]);

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss"></style>
