<template>
  <div>
    <NTooltip>
      <template #trigger>
        <div
          style="
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #2080f0;
            cursor: pointer;
          "
          @click="openFile(item, '2')"
        >
          {{ item.fileName }}
        </div>
      </template>
      <div>{{ item.fileName }}</div>
    </NTooltip>
  </div>
</template>
<script lang="ts" setup>
import { NTooltip } from 'naive-ui';
import { Base64 } from 'js-base64';

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
});

function getApiBase() {
  // return window.location.hostname == 'localhost'
  //   ? 'http://*************:9862'
  //   : window.location.origin;
  if (window.location.hostname == 'localhost') {
    // return 'http://*************:9862';
    return 'https://test-bw.gsafetycloud.com' + '/ehs';
  } else if (window.location.hostname == 'agjp.tanzervas.com') {
    return window.location.origin + '/aqsc/v1' + '/ycsy';
  } else {
    return window.location.origin;
  }
}

const openFile = (file: any, type: string) => {
  console.log('🚀 ~ openFile ~ row,tyoe:', file, type);
  if (file.fileName.includes('doc')) {
    var b64Encoded = Base64.encode(getApiBase() + file.fileUrl);
    window.open(window.$SYS_CFG.apiPdfPreviewURL + encodeURIComponent(b64Encoded));
  } else {
    window.open(getApiBase() + file.fileUrl);
  }
};
defineOptions({ name: 'myTootip' });
</script>
