<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 14:21:35
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-25 16:38:50
 * @FilePath: \jx-paiming\exam-rank\src\views\home\comp\Table.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <n-data-table
    remote
    striped
    :columns="columns"
    :loading="loading"
    :data="tableData"
    :pagination="pagination"
    :render-cell="useEmptyCell"
    :bordered="false"
  >
    <template #empty>
      <div>
        <EmptyComp :text="'暂无数据'" />
      </div>
    </template>
  </n-data-table>

  <AsideComp
    ref="asideRef"
    v-model:show="isShowAside"
    :title="actionLabel"
    :rowData="rowData"
    :isShowAside="isShowAside"
    @refresh="refresh"
  />
  <SummaryView
    :recordId="recordId"
    :unitName="unitName"
    :showModal="showSummaryModal"
    @update:show="showSummaryModal = false"
  />
</template>
<script setup lang="ts">
import SummaryView from '@/views/home/<USER>/summaryView.vue';
import { h, ref, defineOptions, onMounted, defineExpose } from 'vue';
import { useNaivePagination } from '@/common/hooks/useNaivePagination';
import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { IObj } from '@/types/index';
import { debounce } from 'lodash-es';
import { NButton, NTag } from 'naive-ui';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { rankingListAPI } from '@/views/home/<USER>';
import AsideComp from './aside/index.vue';
import EmptyComp from '@/components/empty/Empty.vue';
const isShowAside = ref(false);
const actionLabel = ref('Ces标题');
const asideRef: any = ref(null);

const showSummaryModal = ref(false);
const recordId = ref('');
const unitName = ref('');
const emits = defineEmits(['recordrefresh', 'workflowrefresh']);
const columns = ref([
  {
    title: '排名',
    key: 'index',
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '单位',
    key: 'orgName',
  },
  {
    title: '重大隐患清零',
    key: 'majorHazard',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '1'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.majorHazard
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.majorHazardCount || 0}项`
        )
      );
    },
  },
  {
    title: '重点工作落实',
    key: 'majorWork',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '2'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.majorWork
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.majorWorkCount || 0}项`
        )
      );
    },
  },
  {
    title: '厂站标准化',
    key: 'stationStadard',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '3'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.stationStadard
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.stationStadardCount || 0}项`
        )
      );
    },
  },
  {
    title: '基础管理提升',
    key: 'basicManagementImprove',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '4'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.basicManagementImprove
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.basicManagementImproveCount || 0}项`
        )
      );
    },
  },
  {
    title: '月督查',
    key: 'monthOverseer',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '5'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.monthOverseer
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.monthOverseerCount || 0}项`
        )
      );
    },
  },
  {
    title: '平台运行使用',
    key: 'platformUse',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '6'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.platformUse
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.platformUseCount || 0}项`
        )
      );
    },
  },
  {
    title: '其他',
    key: 'otherItem',
    render(row: any) {
      return h(
        'div',
        {
          onClick: () => open(row, '7'),
          style: {
            cursor: 'pointer',
          },
        },
        // 返回 显示两个字段 颜色不一样
        h(
          'span',
          {
            style: {
              color: '#232323',
            },
          },
          row.otherItem
        ),
        h(
          'span',
          {
            style: {
              marginLeft: '8px',
              color: '#232323',
            },
          },
          `${row.otherItemCount || 0}项`
        )
      );
    },
  },
  {
    title: '扣减总分',
    key: 'deductPointsSummary',
  },
  {
    title: '操作',
    key: 'actions',
    render(row: any) {
      // 标签
      return h(
        NTag,
        {
          type: 'primary',
          onClick: () => handleClick(row),
        },
        { default: () => '汇总查看' }
      );
    },
  },
]);
const handleClick = (row: any) => {
  console.log('🚀 ~ handleClick ~ row:', row);
  unitName.value = row.orgName || '';
  recordId.value = row.id || '';
  showSummaryModal.value = true;
};
const rowData = ref({});
const open = (row: any, type: string) => {
  // if (workflow.value.rankingType == '3' || workflow.value.rankingType == '5' || params.value.flag === 1) return;
  isShowAside.value = true;
  rowData.value = {
    ...row,
    flag: params.value.flag,
    type,
    typeName:
      type == '1'
        ? '重大隐患清零'
        : type == '2'
          ? '重点工作落实'
          : type == '3'
            ? '厂站标准化'
            : type == '4'
              ? '基础管理提升'
              : type == '5'
                ? '月督查'
                : type == '6'
                  ? '平台运行使用'
                  : type == '7'
                    ? '其他'
                    : '--',
  };
  console.log('🚀 ~ open ~ row:', rowData.value);
  actionLabel.value = `【${row.orgName}】${
    type == '1'
      ? '重大隐患清零'
      : type == '2'
        ? '重点工作落实'
        : type == '3'
          ? '厂站标准化'
          : type == '4'
            ? '基础管理提升'
            : type == '5'
              ? '月督查'
              : type == '6'
                ? '平台运行使用'
                : type == '7'
                  ? '其他'
                  : '--'
  }`;
  asideRef.value.getTableData(rowData.value);
};
function refresh() {
  getTableData();
}
const tableData = ref([]); // 排名列表
const rankingNoticeUsers = ref([]); //下发通知人员
const record = ref({}); //排名记录
const workflow = ref<any>({});
const params = ref({});

const [loading, search] = useAutoLoading(true);

const getTableData = debounce(() => {
  params.value = {
    ...filterData,
  };
  loading.value = false;
  search(rankingListAPI(params.value)).then((res: any) => {
    tableData.value = res?.data?.rankingRecordSummaries || [];
    console.log(tableData.value, '--=-=-tableData.value');
    rankingNoticeUsers.value = res?.data?.rankingNoticeUsers || [];
    record.value = res?.data?.record || {};
    workflow.value = res?.data?.workflow || {};
    workflow.value.recordId = res?.data?.record?.id || '';
    emits('workflowrefresh', workflow.value, rankingNoticeUsers.value);
  });
}, 500);

let filterData: IObj<any> = {}; // 搜索条件
function getTableDataWrap(data?: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  console.log(filterData, '-=-=-=-=-=filterData');
  // pagination.page = 1;
  getTableData();
}
onMounted(async () => {
  getTableData();
});
defineExpose({ getTableDataWrap, getTableData });
defineOptions({ name: 'TableIndex' });
</script>
<style lang="scss" scoped>
:deep(.n-data-table .n-data-table-th) {
  background-color: #bbccf3 !important;
}

:deep(.n-data-table .n-data-table-td) {
  background-color: #eef7ff !important;
}

:deep(.n-data-table .n-data-table-tr.n-data-table-tr--striped .n-data-table-td) {
  background: rgba(223, 238, 252, 0.99);
}
</style>
