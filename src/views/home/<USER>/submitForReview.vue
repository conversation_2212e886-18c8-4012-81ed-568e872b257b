<!-- 提交审核 -->
<template>
  <n-modal v-model:show="showModal" @update:show="handleUpdateShow">
    <div>
      <div class="w-[800px] min-h-[500px] bg-[#fff] flex-shrink-0 flex flex-col">
        <div class="flex items-center justify-between gap-[6px] border-b border-[#ccc]">
          <div class="flex items-center gap-[12px] p-[16px] font-bold">
            <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
            提交审核
          </div>
          <div @click="handleUpdateShow(false)">
            <img src="@/assets/images/close.png" alt="" class="w-[50px]" />
          </div>
        </div>

        <div class="flex-1 p-[20px]">
          <n-form class="w-full" ref="formRef" :model="formModel" :rules="rules">
            <n-form-item label="选择审核人" path="reviewerUser">
              <n-select
                v-model:value="formModel.reviewerUser"
                multiple
                label-field="userName"
                value-field="id"
                :options="userOptions"
                :filterable="true"
              />
            </n-form-item>
            <n-form-item label="备注说明" path="remark">
              <n-input v-model:value="formModel.remark" type="textarea" maxlength="100" placeholder="请输入备注说明" />
            </n-form-item>
          </n-form>
        </div>
        <div class="footer flex justify-end gap-[12px] p-[20px]">
          <n-button @click="handleUpdateShow(false)">取消</n-button>
          <n-button type="primary" @click="handleSubmit">提交</n-button>
        </div>
      </div>
    </div>
  </n-modal>
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';

import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { $toast } from '@/common/shareContext';

import { useStore } from '@/store/index';

const userInfo = useStore()?.userInfo;

const props = defineProps({
  showModal: Boolean,
  recordId: String,
});
const emits = defineEmits(['update:show', 'refresh']);
const showModal = computed(() => props.showModal);
const handleUpdateShow = (value: boolean) => {
  emits('update:show', value);
};

const userOptions = ref<{ userName: string; id: string }[]>([]);
const formModel = ref<{
  reviewerUser: string[];
  remark: string;
}>({
  reviewerUser: [],
  remark: '',
});
const formRef = ref();
const rules = ref({
  reviewerUser: [
    {
      type: 'array',
      required: true,
      message: '请选择审核人',
      trigger: ['blur', 'change'],
    },
  ],
});
const loading = ref(false);
// https://test-bw.gsafetycloud.com/api/v3/ehs-exam-rank-service/ranking/getOrgUserList?orgCode=e3de3e5405664eacb434d8e2aafe6d37
const getOrgUserList = async () => {
  const url = api.getUrl(api.type.exam, '/ranking/getOrgUserList', {});
  const res: any = await $http.post(url);
  userOptions.value = res?.data?.rows || [];

  console.log('🚀 ~ getOrgUserList ~ res:', res);
};
const handleSubmit = async () => {
  console.log('🚀 ~ handleSubmit ~ formModel.value:', formModel.value);
  formRef.value?.validate(async (errors: any) => {
    if (errors) {
      // $toast.error('请填写完整信息');
    } else {
      const userNames = userOptions.value
        .filter((item) => formModel.value.reviewerUser.includes(item.id))
        .map((item) => item.userName)
        .join(',');
      console.log('🚀 ~ handleSubmit ~ userNames:', userNames);
      const url = api.getUrl(api.type.exam, '/ranking/updateRankingStatus');
      const res: any = await $http.post(url, {
        data: {
          rankingRecordId: props.recordId,
          rankingRemark: formModel.value.remark,
          // 节点类型 1:待提报 2:退回待提报 3:待审核 4:暂存 5:已审核
          rankingType: '3',
          optUserId: formModel.value.reviewerUser.join(','),
          optUserName: userNames,
          _cfg: { showTip: true, showOkTip: true },
        },
      });
      console.log('🚀 ~ summaryDetail ~ res:', res);
      emits('refresh');
      handleUpdateShow(false);
    }
  });
};
onMounted(() => {
  getOrgUserList();
});
defineOptions({ name: 'SubmitForReview' });
</script>
<style scoped lang="scss"></style>
