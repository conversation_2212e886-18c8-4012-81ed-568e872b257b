<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-24 15:21:05
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-26 16:51:03
 * @FilePath: \jx-paiming\exam-rank\src\views\home\components\adddemeritPoints.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- 提交审核 -->
<template>
  <n-modal v-model:show="showModal" @update:show="handleUpdateShow">
    <div>
      <div
        class="w-[800px] min-h-[500px] bg-[#fff] flex-shrink-0 flex flex-col"
      >
        <div
          class="flex items-center gap-[12px] border-b border-[#ccc] p-[16px] font-bold"
        >
          <img src="@/assets/images/title-icon.png" alt="" class="w-[17px]" />
          {{ title }}扣分项
        </div>
        <div class="flex-1 p-[20px]">
          <div class="">
            <div class="py-[10px]">
              <span style="color: red">*</span>&nbsp;扣分原因
            </div>
            <n-input
              v-model:value="formData.itemReason"
              type="textarea"
              placeholder="请输入扣分原因"
              maxlength="100"
            />
          </div>
          <div>
            <div class="pb-[10px]">
              <span style="color: red">*</span>&nbsp;扣分单位
            </div>
            <n-tree-select
              v-model:value="formData.deductionUnitId"
              placeholder="请选择扣分单位"
              clearable
              label-field="orgName"
              key-field="orgCode"
              children-field="children"
              @update:value="changeFormUnit"
              :options="options"
              :filterable="true"
              :loading="loading"
              @search="handleSearch"
            />
          </div>
          <div class="">
            <div class="py-[10px]">
              <span style="color: red">*</span>&nbsp;扣分值
            </div>
            <n-input
              style="width: 100px"
              v-model:value="formData.deductionValue"
              placeholder="请输入分值"
              :on-update:value="handleNumberInput"
            />
          </div>
          <div class="">
            <div class="py-[10px]">扣分依据</div>
            <FileUpload
              accept=".docx,.pdf,.doc,.jpeg,.jpg,.png"
              :data="fileData"
              @update="handleUpdate"
              :max="3"
              :size="15"
            ></FileUpload>
          </div>
        </div>
        <div class="footer flex justify-end gap-[12px] p-[20px]">
          <n-button @click="close()">取消</n-button>
          <n-button type="primary" @click="submit()">提交</n-button>
        </div>
      </div>
    </div>
  </n-modal>
</template>
<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { FileUpload } from '@/components/upload';
import { getDetail, saveOrUpdate, getOrgCodeList } from '../fetchData.ts';
import useMndCtx from '@/common/shareContext/useMndCtx';

import { useStore } from '@/store/index';
const { message } = useMndCtx();
const info = useStore();
const props = defineProps({
  showModal: Boolean,
  obj: Object,
  title: String,
});
function removeEmptyChildren(obj) {
  if (Array.isArray(obj)) {
    obj.forEach((item) => removeEmptyChildren(item));
  } else if (typeof obj === 'object' && obj !== null) {
    if (obj.children && Array.isArray(obj.children)) {
      if (obj.children.length === 0) {
        delete obj.children;
      } else {
        obj.children.forEach((child) => removeEmptyChildren(child));
      }
    }
    for (const key in obj) {
      removeEmptyChildren(obj[key]);
    }
  }
  return obj;
}

const getUnitData = (orgCode) => {
  getOrgCodeList({ orgCode, needChildUnit: 1, needself: 1, type: 1 }).then(
    (res) => {
      console.log(res, 'unitRes');
      options.value = removeEmptyChildren(res.data);
    }
  );
};
const handleNumberInput = (value: any) => {
  // 1. 检查是否在 0-9999 范围内
  if (value < 0 || value > 9999) {
    message.error('请输入0-9999之间的数字');
    return;
  }

  // 2. 移除所有非数字和非小数点的字符
  const filteredValue = value.replace(/[^\d.]/g, '');

  // 3. 处理多个小数点的情况（如 "12..34" → "12.34"）
  const parts = filteredValue.split('.');
  let formattedValue = parts[0]; // 整数部分

  // 4. 如果有小数部分，限制最多两位
  if (parts.length > 1) {
    formattedValue += '.' + parts[1].slice(0, 2); // 只保留前两位小数
  }

  // 5. 更新表单数据
  formData.value.deductionValue = formattedValue;
};
const emits = defineEmits(['update:show', 'getTableData']);
const fileData = ref([]);
const showModal = computed(() => props.showModal);
function findNodeByOrgCode(tree, orgCode) {
  // 遍历数组中的每一项
  for (const node of tree) {
    // 如果当前节点的 orgCode 匹配，直接返回
    if (node.orgCode === orgCode) {
      return node;
    }
    // 如果有 children，递归查找
    if (node.children && node.children.length > 0) {
      const found = findNodeByOrgCode(node.children, orgCode);
      if (found) return found; // 如果找到，直接返回
    }
  }
  return null; // 没找到返回 null
}

function changeFormUnit(val: string) {
  console.log(val, 'val');
  const obj = findNodeByOrgCode(options.value, val);
  // console.log(obj, '>>>>>');
  formData.value.deductionUnitName = obj.orgName;
}
const openModal = (id) => {
  emits('update:show', true);
};
const getDetailData = (id) => {
  getDetail({ id }).then((res) => {
    console.log(res, '>>>>');
    formData.value = res.data;
    fileData.value = res.data.files;
  });
};
const close = () => {
  handleUpdateShow(false);
  clearForm();
};
const submit = () => {
  if (formData.value.itemReason == '') {
    message.error('请填写扣分原因');
    return;
  }
  if (
    formData.value.deductionUnitId == '' ||
    formData.value.deductionUnitId == null
  ) {
    message.error('请填写扣分单位');
    return;
  }
  if (formData.value.deductionValue == '') {
    message.error('请填写扣分值');
    return;
  }
  saveOrUpdate(formData.value).then((res: any) => {
    // console.log(res, 'res.>>>>>>>>>>>');
    clearForm();
    handleUpdateShow(false);
    // emits('getTableData', props.obj);
  });
};
const clearForm = () => {
  formData.value = {
    deductionUserId: info.userInfo.id,
    deductionUserName: info.userInfo.userName,
    itemSource: props.obj.orgCode,
    deductionValue: '', //扣分值
    itemSourceType: '2',
    summaryRecordId: props.obj.id,
    itemType: props.obj.type,
    itemTypeName: props.obj.typeName,
    itemBaseType: '2',
    itemReason: '', //扣分原因
    deductionUnitName: '', //扣分单位名称
    deductionUnitId: '', //扣分单位id
    files: [], //附件
  };
};
const formData = ref({
  deductionUserId: info.userInfo.id,
  deductionUserName: info.userInfo.userName,
  itemSource: props.obj.orgCode,
  deductionValue: '', //扣分值
  itemSourceType: '2',
  summaryRecordId: props.obj.id,
  itemBaseType: '2',
  itemType: props.obj.type,
  itemTypeName: props.obj.typeName,
  itemReason: '', //扣分原因
  deductionUnitName: '', //扣分单位名称
  deductionUnitId: '', //扣分单位id
  files: [], //附件
});
const handleUpdateShow = (value: boolean) => {
  emits('update:show', value);
};
const handleUpdate = (res: any, data: any) => {
  console.log(data, '��� ~ handleUpdate ~ res');
  // if (!res || !res.length) return;
  // const result = res
  //   .filter((item) => item !== null)
  //   .map((item) => ({ address: item }));

  // // 使用 concat 拼接到 attachments
  // formData.value.filePath = formData.value.filePath.concat(result);

  // // 过滤掉空的附件
  // formData.value.filePath = formData.value.filePath.filter(
  //   (item) => item && item.address // 这里检查 item 是否有效
  // );
  // const uniqueAddresses = new Set();
  // formData.value.filePath = formData.value.filePath.filter((item) => {
  //   const isDuplicate = uniqueAddresses.has(item.address);
  //   uniqueAddresses.add(item.address);
  //   return !isDuplicate; // 返回 false 以删除重复项
  // });
  formData.value.files = res.map((item) => {
    return {
      ...item,
      fileUrl: item.url ? item.url : item.fileUrl,
    };
  });
};
const options = ref([
  // {
  //   value: '01',
  //   label: '张三',
  // },
  // {
  //   value: '02',
  //   label: '李四',
  // },
]);
const loading = ref(false);
const handleSearch = (query: string) => {
  // console.log('🚀 ~ handleSearch ~ query:', query);
  // loading.value = true;
  // setTimeout(() => {
  //   options.value = options.value.filter((item) =>
  //     item.label.toLowerCase().includes(query.toLowerCase())
  //   );
  //   loading.value = false;
  // }, 1000);
};
watch(
  () => props.showModal,
  (val) => {
    if (val) {
      clearForm();
      fileData.value = [];
    }
    console.log(val, 'val');
  },
  { immediate: true }
);
defineExpose({ getDetailData, getUnitData });
defineOptions({ name: 'SubmitForReview' });
</script>
<style scoped lang="scss"></style>
