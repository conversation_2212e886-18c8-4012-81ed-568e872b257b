<template>
  <n-form class="pb-[8px]" :model="filterForm" label-placement="left" inline :show-feedback="false">
    <n-grid :x-gap="6" :y-gap="12" :cols="5">
      <n-gi class="flex items-center">
        <n-form-item label="选择年月:" class="flex-1">
          <n-date-picker
            v-model:value="timestamp"
            type="month"
            format="y年 M月"
            year-format="y年"
            month-format="M月"
            :on-update:value="changeHandle"
            :is-date-disabled="disableFutureDates"
          />
        </n-form-item>
      </n-gi>
      <n-gi class="flex items-center">
        <span class="text-[16px] text-[#FF9E14FF]">{{ recordInfo.rankingType == '5' ? '已发布' : '未发布' }}</span>
      </n-gi>
      <n-gi :span="3">
        <div class="flex justify-end">
          <n-button type="primary" @click="handleExport"> 导出排名 </n-button>
        </div>
      </n-gi>
    </n-grid>
  </n-form>
</template>
<script setup lang="ts">
import { ref, defineProps } from 'vue';
import { ACTION } from '@/views/demo-table/constant';
import { useStore } from '@/store/index';
import { trimObjNull } from '@/utils/obj';
import { throttle } from 'lodash-es';
import { dayjs } from '@/utils/dayjs';
const { userInfo } = useStore();
const props = defineProps({
  recordInfo: Object,
});
// const router = useRouter();
const emits = defineEmits(['action']);
const timestamp = ref(new Date());
const filterForm = ref({
  date: '',
  flag: 1,
});
const disableFutureDates = (ts: number) => {
  const currentDate = new Date();
  const selectedDate = new Date(ts);
  return (
    selectedDate.getFullYear() > currentDate.getFullYear() ||
    (selectedDate.getFullYear() === currentDate.getFullYear() && selectedDate.getMonth() > currentDate.getMonth())
  );
};
function changeHandle(value: number | null, formattedValue: string | null) {
  timestamp.value = value;
  filterForm.value.date = dayjs(value).format('YYYY-MM');
  doHandle();
}
const doHandle = throttle(() => {
  emits('action', {
    action: ACTION.SEARCH,
    data: trimObjNull(filterForm.value),
  });
}, 1000);
// 导出
function handleExport() {
  filterForm.value.date = dayjs(timestamp.value).format('YYYY-MM');
  emits('action', {
    action: ACTION.EXPORT,
    data: trimObjNull(filterForm.value),
  });
}
function getVal() {
  return filterForm.value;
}
defineExpose({ getVal });
defineOptions({ name: 'HistoryFilter' });
</script>
<style lang="scss" scoped></style>
