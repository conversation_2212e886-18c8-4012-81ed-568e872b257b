<template>
  <n-layout has-sider class="com-menu menuBox">
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="240"
      @collapse="setExpand(true)"
      @expand="setExpand(true)"
      :bordered="false"
      collapse-mode="width"
    >
      <div
        class="fold"
        :onClick="foldClick"
        :style="{ 'padding-left': isExpand ? '14px' : '18px', height: '48px', fontSize: '16px' }"
      >
        <img :src="getImg()" alt="" style="width: 20px; height: 20px" />
        <div class="text" v-if="!isExpand" style="font-size: 16px; padding-left: 10px">安全考核排名</div>
      </div>
      <n-scrollbar :style="`max-height:calc(100vh - 50px - 48px)`">
        <n-menu
          ref="menuInstRef"
          class="com-menu"
          v-model:value="activeKey"
          :default-expand-all="false"
          :collapsed="isExpand"
          :options="filteredMenuList"
          label-field="resName"
          key-field="resUrl"
          children-field="childrens"
          :render-icon="renderMenuIcon"
          :collapsed-icon-size="18"
          :inverted="isExpand"
          :accordion="false"
          :root-indent="18"
          @update:value="handleUpdateValue"
          :style="{ fontSize: '16px', '--n-item-color-active-hover': '#527cff', '--n-item-height': '48px' }"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
import { useStore } from '@/store';
import type { ILoginRes } from '@/store/type';
import { trimLastEmptyChild } from '@/utils/treeMenu.ts';
import { useMenu } from '@/views/menu/useMenu';
import { getLogin } from '@/api/login';
import type { MenuInst } from 'naive-ui';
import { computed, h, onBeforeMount, ref, watch, nextTick, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { $Config } from '@tanzerfe/http';
import { useState } from '@/common/hooks/useState';
import { menuDataList } from './menu';

const menuInstRef = ref<MenuInst | null>(null);
const router = useRouter();
const expandedKeys = ref([]);
watch(
  () => router.currentRoute.value,
  (val: any) => {
    if (val.name) {
      let key = val.name;
      if (
        (val.meta?.activePname && val.meta?.activePname === 'dynamicControl') ||
        (val.meta?.activePname === 'controlPlan' && val.name === 'controlPlanInfo')
      ) {
        key = val.meta?.activePname;
      }
      nextTick(() => {
        setTimeout(() => {
          menuInstRef.value?.showOption(key);
        }, 1000);
      });
    }
  },
  {
    immediate: true,
  }
);
const getImg = () => {
  return isExpand.value
    ? new URL(`@/assets/foldTurn.png`, import.meta.url).href
    : new URL(`@/assets/fold.png`, import.meta.url).href;
};
const getStore = ref(useStore());
const route: any = useRoute();
const { menuList, activeKey, handleUpdateValue } = useMenu();
const [isExpand, setExpand] = useState(false);
const filteredMenuList: any = ref([]);

const initMenuList = () => {
  // 本地调试本地路由
  // filteredMenuList.value = menuList;
  // 线上获取路由
  if (route.query?.token) {
    // 获取登录前重置store
    getStore.value.reset();
    goLogin(route.query?.sysCode, route.query?.token);
  } else {
    filteredMenuList.value = delIcon(menuDataList);
    console.log('menuList', filteredMenuList.value);
    // filteredMenuList.value = delIcon(getStore.value.userInfo.resourceVoList);
  }
};
const foldClick = () => {
  isExpand.value = !isExpand.value;
  // getStore.value.isExpand = isExpand.value;
};
// 批量渲染菜单svg图标
function renderMenuIcon(option: any) {
  if (option.resIcon) {
    return h(
      'img',
      {
        src: window.$SYS_CFG.base_url + '/' + option.resIconUrl,
        width: 24,
        height: 24,
      },
      { default: () => null }
    );
  } else {
    return '';
  }
}
const props = defineProps({
  headless: Boolean,
});

const scrollMaxH = computed(() => {
  return 'calc(100vh - 48px - 48px)';
});
const goLogin = (sysCode: any, token: any) => {
  getStore.value.query.querySysCode = sysCode;
  getStore.value.query.queryToken = token;
  getStore.value.userInfo = {};
  const _params: any = {
    sysCode: sysCode,
    token: token,
  };

  getLogin(_params).then(async (res: any) => {
    if (res.code != 'success') {
      window.location.href = `${window.location.origin}${window.$SYS_CFG.base_url}/platform/#/login`;
      return;
    }
    const _use: ILoginRes | any = res?.data;
    _use.resourceVoList = trimLastEmptyChild(_use.resourceVoList, 'childrens');
    getStore.value.userInfo = _use;
    $Config.getCustomHeaders = {
      Token: res?.data.token,
      Zhid: res?.data.zhId,
    };

    filteredMenuList.value = delIcon(getStore.value.userInfo.resourceVoList);
  });
};
//递归删除icon返回数组
const delIcon = (arr: any) => {
  arr.forEach((item: any) => {
    delete item.icon;
    if (item.childrens && item.childrens.length > 0) {
      delIcon(item.childrens);
    }
  });
  return arr;
};

onBeforeMount(() => {
  initMenuList();
});
defineOptions({ name: 'MenuIndex' });
</script>

<style module lang="scss">
.wrap {
  //background: var(--com-box-bg);
  background-color: #252843 !important;

  &.expand {
    // background: var(--com-primary-color);
    background: #252843 !important;
  }
}
</style>
<style scoped lang="scss">
.fold {
  width: 100%;
  // height:48px;
  // font-size: 16px;
  overflow: hidden;
  align-items: center;
  background-color: #252843;
  cursor: pointer;
  transition: all 0.45s ease;
  display: flex;
  color: #fff;

  .text {
    // padding-left: 10px;
    transition: all 0.45s ease;
  }

  img {
    // width: 20px;
    // height: 20px;
  }
}

:deep(.n-layout-sider-scroll-container) {
  overflow: hidden !important;
}

:deep(.n-menu .n-submenu .n-submenu-children) {
  background-color: #363d64;
}

:deep(.n-menu .n-menu-item-content.n-menu-item-content--selected) {
  &::before {
    background: #527cff;
  }
  &::after {
    content: '';
    position: absolute;
    right: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #c8d5ff;
  }
}

:deep(.n-menu .n-menu-item-content .n-menu-item-content__arrow) {
  transform: rotate(0deg);
  color: #fff;
}
:deep(.n-menu .n-menu-item-content.n-menu-item-content--collapsed .n-menu-item-content__arrow) {
  transform: rotate(-90deg);
  color: #fff;
}
:deep(.n-menu .n-menu-item-content.n-menu-item-content--selected .n-menu-item-content-header) {
  color: #fff;
}
</style>
