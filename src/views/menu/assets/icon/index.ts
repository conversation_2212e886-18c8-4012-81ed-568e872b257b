import { iconBase } from '@/utils/svg.ts';

export const icon_expand = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="a"><rect width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#a)"><path d="M1.115 10.388l4.228 3.027c.**************.285.02a.277.277 0 0 0 .147-.249v-6.05a.277.277 0 0 0-.147-.248.271.271 0 0 0-.285.02L1.115 9.935a.28.28 0 0 0 0 .452zM8.125 6.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm0 4.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm-6.333 4.8h16.416q.792 0 .792.8t-.792.8H1.792Q1 18 1 17.2t.792-.8zm0-14.4h16.416Q19 2 19 2.8t-.792.8H1.792Q1 3.6 1 2.8t.792-.8z" fill="#606266"/></g></svg>`
  );

export const icon_sy = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><g><g><path d="M11.5,17L11.5,13.6766C11.5,13.1521,11.05229,12.72701,10.5,12.72701L9.5,12.72701C8.94771,12.72701,8.5,13.1521,8.5,13.6766L8.5,17L4,17C3.447715,17,3,16.5749,3,16.0504L3,8.2812C3.0000698853,7.98449,3.1462,7.70487,3.395,7.52536L9.395,3.193495C9.75258,2.9355018,10.24742,2.9355018,10.605,3.193495L16.605,7.52536C16.8539,7.705,17,7.98462,17,8.2812L17,16.0504C17,16.5749,16.552300000000002,17,16,17L11.5,17Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );

export const icon_pzgl = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_8384"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_8384)"><g><path d="M17.26914,13.38107L11.730858,13.38107C11.324826,13.38107,11,13.07417,11,12.690536999999999C11,12.306905,11.324826,12,11.730858,12L17.26914,12C17.67517,12,18,12.306905,18,12.690536999999999C18,13.07417,17.67517,13.38107,17.26914,13.38107ZM17.26914,15.69821L11.730858,15.69821C11.324826,15.69821,11,15.391300000000001,11,15.007670000000001C11,14.62404,11.324826,14.31714,11.730858,14.31714L17.26914,14.31714C17.67517,14.31714,18,14.62404,18,15.007670000000001C18,15.391300000000001,17.67517,15.69821,17.26914,15.69821ZM17.26914,18L11.730858,18C11.324826,18,11,17.693089999999998,11,17.30946C11,16.92583,11.324826,16.61893,11.730858,16.61893L17.26914,16.61893C17.67517,16.61893,18,16.92583,18,17.30946C18,17.693089999999998,17.67517,18,17.26914,18Z" fill="#545A67" fill-opacity="1"/></g><g><path d="M17.8602,7.19424L16.4621,4.82156C16.1857,4.37268,15.6005,4.19633,15.1291,4.4368099999999995L13.9911,4.88569C13.5684,4.56506,13.0969,4.30855,12.593,4.11617L12.4304,2.945865C12.3979,2.4007899999999998,11.95895,2,11.42248,2L8.593779999999999,2C8.0573,2,7.61836,2.4007899999999998,7.58585,2.929834L7.42328,4.10014C6.93557,4.30855,6.46412,4.56506,6.02519,4.86966L4.88721,4.42077C4.415760000000001,4.1803,3.8305100000000003,4.35665,3.5541400000000003,4.80553L2.1397909999999998,7.19424C1.87968,7.64313,2.0259924,8.2363,2.481185,8.50884L3.44034,9.230260000000001C3.37531,9.743269999999999,3.37531,10.27231,3.44034,10.78533L2.464929,11.50675C2.00973549,11.77929,1.863423,12.3725,2.1397909999999998,12.8213L3.5541400000000003,15.194C3.8305100000000003,15.6429,4.415760000000001,15.8193,4.90346,15.5788L6.02519,15.1299C6.44787,15.4505,6.91932,15.707,7.40702,15.8994L7.56959,17.069699999999997C7.60211,17.5988,8.041039999999999,18.0156,8.57752,17.9996L9.69925,17.9996C9.86182,17.9996,9.97561,17.871299999999998,9.991869999999999,17.727L9.991869999999999,17.117800000000003L9.991869999999999,13.2542C8.18736,13.2702,6.70798,11.81135,6.70798,10.01581C6.70798,8.22027,8.18736,6.76139,10.00813,6.76139C11.8289,6.76139,13.3083,8.22027,13.3083,10.01581C13.3083,10.25628,13.2758,10.49676,13.227,10.73723L16.3808,10.73723C16.4946,10.73723,16.5922,10.64104,16.6084,10.52882C16.640900000000002,10.09597,16.640900000000002,9.67915,16.5759,9.24629L17.5351,8.52487C17.990299999999998,8.2363,18.1366,7.64313,17.8602,7.19424Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );
