<template>
  <div :class="$style['avatar']">
    <div :class="$style['img']">{{ userInitial }}</div>
    <n-ellipsis v-if="userName" class="!max-w-[80px]" :tooltip="false" :title="userName.length > 6 ? userName : ''">
      {{ userName }}
    </n-ellipsis>
  </div>
</template>

<script lang="ts" setup>
import { NEllipsis } from 'naive-ui';
import { defineComponent } from 'vue';
import { computed, ref, watch, h, onMounted } from 'vue';
import { useStore } from '@/store';
const getStore = ref(useStore());

const emits = defineEmits(['show-downloadApp']);

const userName = ref('');
const userInitial = computed(() => {
  const name = getStore.value.userInfo.userName || '系统管理员';
  return name.length >= 2 ? name.slice(-2) : name;
});
watch(
  () => getStore.value.userInfo,
  (val: any) => {
    userName.value = val?.userName || '系统管理员';
  },
  { deep: true, immediate: true }
);
defineComponent({ name: 'avatarComp' });
</script>

<style module lang="scss">
.avatar {
  display: grid;
  align-items: center;
  grid-template-columns: auto 1fr;
  column-gap: 12px;

  .img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 50%;
    background-color: #527cff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}
</style>
