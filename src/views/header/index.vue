<!--
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-11 10:19:57
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2024-09-11 11:02:07
 * @FilePath: \ehs-risk-mgr\src\views\header\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <header class="com-header" :class="$style['header']">
    <div :class="$style['area']">
      <!--  left  -->
      <div :class="$style['area-l']" class="flex items-center">
        <img class="mr-[5px] imgBoxIcon" :src="getImg()" alt="" srcset="" />
        <span :class="$style['title']" class="text-[30px] font-['youshe'] cursor-pointer" @click="openWorkPlatform">{{
          logoName ? logoName : '安全考核排名'
        }}</span>
      </div>
      <!--  right  -->
      <div :class="$style['area-r']">
        <!-- <div class="flex items-center justify-center cursor-pointer" @click="openGis">
          <img src="./assets/gis_icon.png" class="w-[36px] h-[36px]" alt="" />
        </div> -->
        <avatar />
      </div>
    </div>
  </header>
</template>

<script lang="ts" setup>
import { useStore } from '@/store';
import { ref, watch } from 'vue';
import Avatar from './comp/Avatar.vue';
import { getIcoImg, getLogoImg } from './headerFn.ts';
const getStore = ref(useStore());

const logoName = ref(getStore.value.userInfo.zhName);
const getCreate = () => {
  var link: any = document.querySelector("link[rel*='icon']") || document.createElement('link');
  link.type = 'image/x-icon';
  link.rel = 'shortcut icon';
  link.href = getIcoImg();
  document.getElementsByTagName('head')[0].appendChild(link);
  document.title = getStore.value.userInfo.zhName ? getStore.value.userInfo.zhName : '安全考核排名';
  // document.title = '安全生产排名';
};

const getImg = () => {
  return getLogoImg();
};

// const openGis = () => {
//   // 测试打开 gis 屏幕
//   const _host = window.location.origin;
//   window.open(`${_host}${window.$SYS_CFG.base_url}/ehs-gis/index.html#/?project=ehsRiskMgr`, 'risk-gisWindow');
// };

const openWorkPlatform = () => {
  const url = JSON.parse(localStorage.getItem('exam-rank-store') as string).userInfo.zhPlatformUrl;
  if (!url) {
    throw new Error('未配置工作平台地址!');
  }
  // location.href = url;
  window.open(url, 'work-platform');
};

watch(
  () => useStore()?.userInfo,
  (nv) => {
    logoName.value = nv.zhName;
    if (nv) {
      getCreate();
    }
  },
  { deep: true, immediate: true }
);

defineOptions({ name: 'MisHeaderComp' });
</script>

<style module lang="scss">
.header {
  position: relative;
  overflow: hidden;
  background: #252843;
}

.area {
  position: relative;
  height: 100%;
  display: grid;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

.area-l {
  position: relative;
  font-size: 24px;
  /* padding-left: 45px; */
}

.area-r {
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 20px;
}

.title {
  color: #fff;

  font-size: 30px;
  font-family: 'YouSheBiaoTiHei', sans-serif;

  /* font-family: 'youshe',serif;
  text-shadow: 0px 2px 8px rgba(41,47,58,0.2016); */
}
</style>
<style scoped lang="scss">
.imgBoxIcon {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}
</style>
