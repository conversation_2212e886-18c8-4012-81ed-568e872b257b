import useToastCtx from '@/common/shareContext/useToastCtx.ts';
import { useStore } from '@/store';
import { $Config } from '@tanzerfe/http';

export function setupApi() {
  const store = useStore();

  //监听token变化
  // store.$subscribe((mutation, state) => {
  //   $Config.getCustomHeaders = {
  //     Token: state.userInfo.token,
  //     Zhid: state.userInfo.zhId,
  //   };
  // });
  $Config.getCustomHeaders = {
    Token: store.userInfo.token,
    Zhid: store.userInfo.zhId,
  };

  $Config.responseHandler = function (resData: any, reqData: any) {
    if (resData.code == '401') {
      return (window.location.href = `${window.location.origin}${window.$SYS_CFG.base_url}/platform/#/login`);
    }
  };
  $Config.requestErrorHandler = (error: any) => {
    // 错误捕获接口被网关拦截，刷新页面重新获取Cookie
    if (!error.response || !error.response?.status) {
      location.reload();
    }
  };
  $Config.$toastDark = useToastCtx({ theme: 'dark' });
  $Config.$toastLight = useToastCtx({ theme: 'light' });
}
