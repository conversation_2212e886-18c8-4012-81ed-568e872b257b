<template>
  <n-breadcrumb :theme-overrides="breadcrumbTheme" :style="`height:${toVw(45)};`">
    <n-breadcrumb-item
      class="text"
      style="height: 100%"
      v-for="item of data"
      :clickable="!!item.clickable"
      :key="item.name"
      @click="handleClick(item)"
    >
      {{ item.name }}
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<script lang="ts" setup>
import { toVw, toVh } from '@/utils/fit';
import breadcrumbTheme from './theme';
import { useRouter } from 'vue-router';
import type { IBreadData } from '@/components/breadcrumb/type.ts';

interface Props {
  data: IBreadData[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
});
const router = useRouter();

/**
 * 路由跳转
 * @param item
 */
function handleClick(item: IBreadData) {
  if (item.clickable && item.routeRaw) {
    router.push(item.routeRaw);
  }
}

defineOptions({ name: 'ComBread' }); // 面包屑组件
</script>

<style scoped lang="scss">
.text {
  font-size: 14px !important;
}
:deep(ul) {
  height: 100%;
  display: flex;
}
:deep(.n-breadcrumb-item__link) {
  height: 100%;
  padding: 0px !important;
  display: flex;
  align-items: center;
}
:deep(.n-breadcrumb-item__separator) {
  display: flex;
  align-items: center;
  height: 100%;
  margin: 0 8px !important;
}
</style>
