import { BreadcrumbProps } from 'naive-ui';

type BreadcrumbThemeOverrides = NonNullable<BreadcrumbProps['themeOverrides']>;
const breadcrumbTheme: BreadcrumbThemeOverrides = {
  fontSize: '16px',
  itemLineHeight: '37px',
  itemTextColor: '#9EA4AA',
  itemTextColorHover: '#333639',
  itemTextColorPressed: '#333639',
  itemColorPressed: '#333639',
  itemTextColorActive: '#333639',
  separatorColor: '#9EA4AA',
};

export default breadcrumbTheme;
