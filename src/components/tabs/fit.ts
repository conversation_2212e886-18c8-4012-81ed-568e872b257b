import Big from 'big.js';

const variables = {
  screenDesignWidth: 1920,
  screenDesignHeight: 1080,
};
/** Echarts图表字体、间距自适应 */
export const fitChartSize = (size: number, defalteWidth = Number(variables.screenDesignWidth)) => {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return size;
  const scale = clientWidth / defalteWidth;
  return Number((size * scale).toFixed(5));
};

/** 转换为vw单位 */
export const toVw = (size: number, defalteWidth = Number(variables.screenDesignWidth)) => {
  return Number(new Big(size).div(defalteWidth).times(100).toFixed(5, 0)) + 'vw';
};

/** 转换为vh单位 */
export const toVh = (size: number, defalteHeight = Number(variables.screenDesignHeight)) => {
  return Number(new Big(size).div(defalteHeight).times(100).toFixed(5, 0)) + 'vh';
};
