/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-23 10:03:44
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-24 16:10:09
 * @FilePath: \jx-paiming\exam-rank\src\common\shareContext\useDialogCtx.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { DialogOptions } from 'naive-ui';
import useMndCtx from './useMndCtx';

export default function useDialogCtx(dialogOptions?: DialogOptions) {
  const ins = useMndCtx().dialog;
  const createOpt = (): DialogOptions => {
    return {
      title: '温馨提醒',
      showIcon: true,
      ...dialogOptions,
    };
  };

  // 包装一层用于自定义
  return {
    info: (options?: DialogOptions) => ins.info({ ...createOpt(), ...options }),
    success: (options?: DialogOptions) =>
      ins.success({ ...createOpt(), ...options }),
    warning: (options?: DialogOptions) =>
      ins.warning({ ...createOpt(), ...options }),
    error: (options?: DialogOptions) =>
      ins.error({ ...createOpt(), ...options }),
    destroyAll: () => ins.destroyAll(),
  };
}
export const $dialog = useDialogCtx();
