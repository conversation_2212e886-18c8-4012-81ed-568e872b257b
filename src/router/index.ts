import { createRouter, createWebHashHistory } from 'vue-router';
import routes from './routes';

const Router = createRouter({
  scrollBehavior: () => ({
    left: 0,
    top: 0,
  }),
  routes,
  history: createWebHashHistory(),
});

// Router.beforeEach((to, from, next) => {
//   const token: string = (JSON.parse(localStorage.getItem('ehs-risk-mgr-store') as string))?.userInfo?.token;
//   //有token直接跳转没有token跳转登录
//   if (token) {
//     next();
//   }else{
//     location.href ='http://223.247.156.85:9832/platform/#/login';
//     next();
//   }
// })

export default Router;
