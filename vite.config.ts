/*
 * @Author: fang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-11 10:19:57
 * @LastEditors: fangweiwei <EMAIL>
 * @LastEditTime: 2025-04-08 09:45:42
 * @FilePath: \ehs-risk-mgr\vite.config.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { webUpdateNotice } from '@plugin-web-update-notification/vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  server: {
    host: true,
    port: 5177,
    proxy: {
      '/api/v3': {
        // target: 'https://agjp.tanzervas.com/aqsc/v1', // 演示环境gis
        target: 'https://test-bw.gsafetycloud.com',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'ehs-exam-rank-web',
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
            @import 'src/css/utils.scss';
            @import "src/css/variables.scss";
          `,
        api: 'modern-compiler',
      },
    },
  },
  plugins: [
    vue(),
    webUpdateNotice({
      logVersion: true,
      versionType: 'build_timestamp',
      checkInterval: 20 * 1000, // 设置检查间隔，单位毫秒，5秒
      notificationProps: {
        title: '系统更新',
        description: '系统已更新，请刷新页面',
        buttonText: '刷新',
        dismissButtonText: '忽略',
      },
    }),
    Components({
      resolvers: [NaiveUiResolver()],
      directoryAsNamespace: true,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      src: resolve(__dirname, 'src'),
    },
  },
});
