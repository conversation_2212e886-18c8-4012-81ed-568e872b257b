/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-06-23 10:04:28
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-06-25 15:24:11
 * @FilePath: \jx-paiming\exam-rank\public\com\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
function getBaseUrl() {
  console.log('🚀 ~ getBaseUrl:');
  // demo: https://agjp.tanzervas.com/aqsc/v1/rmc/index.html#/risk-card
  // 截取 /aqsc/v1
  const str = window.location.pathname;
  // 找到最后一个斜杠的索引
  const lastIndex = str.lastIndexOf('/');
  // 找到倒数第二个斜杠的索引
  const secondLastIndex = str.lastIndexOf('/', lastIndex - 1);
  // 截取倒数第二个斜杠之前的内容
  return secondLastIndex !== -1 ? str.substring(0, secondLastIndex) : str;
}

window.$SYS_CFG = {
  env: 'dev',
  version: {},
  // apiBaseURL: "/PROXY", // 本地代理
  // base_url: '', // 本地开发
  // baseWeatherUrl:'/api/v3', //获取天气接口
  // parentHostPath: 'https://test-bw.gsafetycloud.com',
  base_url: getBaseUrl(), // 通用打包，支持开发环境+演示环境
  apiBaseURL: getBaseUrl() + '/api/v3', // 通用打包，支持开发环境+演示环境
  apiPdfPreviewURL: 'http://**************:9092/onlinePreview?url=',
};
